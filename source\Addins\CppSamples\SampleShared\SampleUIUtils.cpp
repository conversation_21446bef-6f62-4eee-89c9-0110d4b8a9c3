﻿#include "SampleUIUtils.h"
#include "IRibbon.h"
#include "IUiView.h"
#include "ICanvas.h"
#include "GbmpNew.h"
#include "IControl.h"
#include "IDocument.h"
#include "ICategory.h"
#include "IStatusBar.h"
#include "IModelView.h"
#include "IUiManager.h"
#include "IMainWindow.h"
#include "IUiDocument.h"
#include "IRibbonPage.h"
#include "IPickTarget.h"
#include "IApplication.h"
#include "IRibbonGroup.h"
#include "IDockManager.h"
#include "IPageHandler.h"
#include "Vector3dUtils.h"
#include "GcmpActionBase.h"
#include "IDrawingLayout.h"
#include "UiCommonDialog.h"
#include "IStatusBarPanel.h"
#include "IPropertyWidget.h"
#include "UiInterfaceEnums.h"
#include "ILabelDefinition.h"
#include "IUserTransaction.h"
#include "IDrawingViewport.h"
#include "ICategoryLibrary.h"
#include "GcmpCommandState.h"
#include "IGalleryDefinition.h"
#include "UiDocumentViewUtils.h"
#include "IRibbonContextualPage.h"
#include "IRibbonContextualPage.h"
#include "IDrawingLayoutManager.h"
#include "ICheckBoxGroupHandler.h"
#include "IUiDocumentViewManager.h"
#include "ICheckBoxGroupEventArgs.h"
#include "ICommandButtonDefinition.h"
#include "ICheckBoxGroupDefinition.h"
#include "ICommandMenuItemDefinition.h"
#include "ICommandButtonMenuDefinition.h"

#include "CommandIds.h"
#include "SampleGroupHandler.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

gcmp::IMainWindow* SampleUIUtils::GetMainWindow()
{
    IApplication *pApplication = IApplication::Get();
    if (!pApplication)
    {
        return nullptr;
    }
    return IMainWindow::GetMainWindow();
}

gcmp::IUiDocumentViewManager* SampleUIUtils::GetUiDocumentViewManager()
{
    IMainWindow *pMainWindow = GetMainWindow();
    if (!pMainWindow)
    {
        return nullptr;
    }
    return IUiDocumentViewManager::Get();
}

gcmp::IDockManager* SampleUIUtils::GetDockManager()
{
    IMainWindow *pMainWindow = GetMainWindow();
    if (!pMainWindow)
    {
        return nullptr;
    }
    return IUiManager::Get()->GetDockManager();
}

bool SampleUIUtils::IsInDrawingEnvironment()
{
    if (UiDocumentViewUtils::GetCurrentDocument() == nullptr)
    {
        return false;
    }
    IModelView *pModelView = UiDocumentViewUtils::GetCurrentModelView();
    if (!pModelView)
    {
        return false;
    }
    return pModelView->GetViewType() == BuiltInViewType::Drawing;
}

bool SampleUIUtils::IsInProjectEnvironment()
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (pDoc == nullptr)
    {
        return false;
    }
    return pDoc->IsFamilyDocument() ? false : true;
}

void SampleUIUtils::SetupActionContext(gcmp::IAction *pAction, const std::wstring &pageName,
    const std::wstring& contextualPageCaption,
    const std::vector<CommandContextDefinition> &definitions)
{
    OwnerPtr<IRibbonGroup> opGroup = IRibbonGroup::Create(L"demoRuntimeDynamicGroup", L"绘制");
    OwnerPtr<IGalleryDefinition> opGalleryDefine = IGalleryDefinition::Create(L"demoRuntimeDynamicGallery", L"几何方法");
    for (auto item : definitions)
    {
        opGalleryDefine->AddItem(item.CommandId, item.Name, item.Tooltip, item.IconPath, std::wstring());
    }
    opGalleryDefine->SetCurrentGalleryItem(definitions[0].CommandId);
    opGroup->AddGallery(TransferOwnership(opGalleryDefine));
    std::vector<gcmp::OwnerPtr<IRibbonGroup>> controlGroups;
    controlGroups.emplace_back(TransferOwnership(opGroup));
    GcmpActionBase *pActionBase = dynamic_cast<GcmpActionBase*>(pAction);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pActionBase, L"pActionBase为空", L"GDMPLab", L"2024-03-30");
    pActionBase->AddDynamicGroup(pageName, TransferOwnership(controlGroups), contextualPageCaption);
}

extern void Sample::AddGRepProbeButtons(OwnerPtr<IRibbonGroup>& opGrepGroup)
{
    std::wstring tooltipGRepProbe = L"读取选择图元的模型图形表达和几何造型数据。如果选择图元为IDirectShape或IPureGraphicsElement，那么可以修改图元的模型图形表达；其他类型图元可以用“复制GRep为可编辑”生成可修改的IDirectShape或IPureGraphicsElement";
    OwnerPtr<ICommandButtonMenuDefinition> opGrepProbeButtonMenuDefinition =
        ICommandButtonMenuDefinition::Create(ID_CMD_GREP_PROBE, ID_CMD_GREP_PROBE,
            GBMP_TR(L"模型图形表达探针"), L":/images/Module/Grep/grep_tree.png",
            GBMP_TR(tooltipGRepProbe));
    DBG_WARN_AND_RETURN_VOID_UNLESS(opGrepProbeButtonMenuDefinition, L"opGrepProbeButtonMenuDefinition为空", L"GDMPLab", L"2024-03-30");
    {
        OwnerPtr<ICommandMenuItemDefinition> opGrepProbeButtonDefinition =
            ICommandMenuItemDefinition::Create(ID_CMD_GREP_PROBE, ID_CMD_GREP_PROBE, GBMP_TR(L"模型图形表达探针"),
                GBMP_TR(tooltipGRepProbe),
                L":/images/Module/Grep/grep_tree.png");
        DBG_WARN_AND_RETURN_VOID_UNLESS(opGrepProbeButtonDefinition, L"opGrepProbeButtonDefinition为空", L"GDMPLab", L"2024-03-30");
        opGrepProbeButtonMenuDefinition->AddItem(TransferOwnership(opGrepProbeButtonDefinition));

        OwnerPtr<ICommandMenuItemDefinition> opViewSpecificGrepProbeButtonDefinition =
            ICommandMenuItemDefinition::Create(ID_CMD_VIEW_SPECIFIC_GREP_PROBE, ID_CMD_VIEW_SPECIFIC_GREP_PROBE, GBMP_TR(L"视图组件图形表达探针"),
                GBMP_TR(L"读取选择图元的视图组件图形表达和几何造型数据。视图组件图形表达不可修改，可以用“复制视图组件GRep为可编辑”生成可修改的IDirectShape或IPureGraphicsElement后，修改生成图元的模型图形表达"),
                L":/images/Module/Grep/viewSpecific.png");
        DBG_WARN_AND_RETURN_VOID_UNLESS(opViewSpecificGrepProbeButtonDefinition, L"opViewGrepProbeButtonDefinition为空", L"GDMPLab", L"2024-03-30");
        opGrepProbeButtonMenuDefinition->AddItem(TransferOwnership(opViewSpecificGrepProbeButtonDefinition));

        OwnerPtr<ICommandMenuItemDefinition> opViewGrepProbeButtonDefinition =
            ICommandMenuItemDefinition::Create(ID_CMD_VIEW_GREP_PROBE, ID_CMD_VIEW_GREP_PROBE, GBMP_TR(L"视图图形表达探针"),
                GBMP_TR(L"读取选择图元在当前视图的视图图形表达和几何造型数据。视图图形表达不可修改。"), L":/images/Module/Grep/viewSpecific.png");
        DBG_WARN_AND_RETURN_VOID_UNLESS(opViewGrepProbeButtonDefinition, L"opCreateGNodeIdButtonItemDefinition为空", L"GDMPLab", L"2024-03-30");
        opGrepProbeButtonMenuDefinition->AddItem(TransferOwnership(opViewGrepProbeButtonDefinition));
    }
    opGrepGroup->AddButtonMenu(TransferOwnership(opGrepProbeButtonMenuDefinition));
}

gcmp::IModelView* GetOrCreateModelViewDrawing(gcmp::IDocument* pDocument, const std::wstring viewName/*L"ModelViewForDrawing"*/)
{
    gcmp::IModelView* pViewDrawing = nullptr;
    if (nullptr == pDocument)
    {
        return pViewDrawing;
    }

    for each (gcmp::IModelView* pModelView in gcmp::IModelView::GetAllModelViews(pDocument))
    {
        if (gcmp::BuiltInViewType::Drawing != pModelView->GetViewType())
        {
            continue;
        }

        if (!(gcmp::Vector3dUtils::IsEqual(Vector3d(1, 0, 0), pModelView->GetRightDirection()) && gcmp::Vector3dUtils::IsEqual(Vector3d(0, 1, 0), pModelView->GetUpDirection())))
        {
            continue;
        }

        if (pModelView->GetName() == viewName)
        {
            pViewDrawing = pModelView;
            break;
        }
    }

    if (nullptr == pViewDrawing)
    {
        OwnerPtr<IUserTransaction> opUserTransaction = gcmp::IUserTransaction::Create(pDocument, L"创建图纸视图");

        gcmp::IDrawingLayoutManager* pDrawingLayoutMgr = gcmp::IDrawingLayoutManager::Get(pDocument);
        if (nullptr == pDrawingLayoutMgr)
        {
            opUserTransaction->Rollback();
            return nullptr;
        }
        std::wstring errorMsg = L"";
        gcmp::IDrawingLayout* pLayout = pDrawingLayoutMgr->CreateLayout(viewName, &errorMsg);
        if (nullptr == pLayout)
        {
            opUserTransaction->Rollback();
            UiCommonDialog::ShowMessageBox(GBMP_TR(L"发生错误"), errorMsg, (int)UiCommonDialog::ButtonType::OK);
            return nullptr;
        }

        pViewDrawing = pLayout->GetModelView();
        gcmp::ICategoryLibrary* pCategoryLibrary = gcmp::ICategoryLibrary::Get(pDocument);
        if (nullptr != pCategoryLibrary)
        {
            for (const gcmp::ICategory* pCategory : pCategoryLibrary->GetAllCategories())
            {
                const std::wstring categoryName = pCategory->GetName();
                if (categoryName == L"模型线" || categoryName == L"定制直线" || categoryName == L"临时对象样式")
                {
                    pViewDrawing->SetElementsVisibilityByCategoryUid(pCategory->GetUniIdentity(), false);
                }
            }
        }
        pViewDrawing->SetViewScale(5.0);
        pViewDrawing->SetDetailLevel(ModelViewDetailLevel::Fine);
        opUserTransaction->Commit();
    }

    return pViewDrawing;
}

bool CheckDrawingLayoutAndViewPort(const gcmp::IModelView* pModelViewDrawing)
{
    if (nullptr == pModelViewDrawing)
    {
        return false;
    }
    if (pModelViewDrawing->GetViewType() != BuiltInViewType::Drawing && pModelViewDrawing->GetViewType() != BuiltInViewType::Perspective)
    {
        return false;
    }

    gcmp::IDrawingLayoutManager* pDrawingLayoutManager = gcmp::IDrawingLayoutManager::Get(pModelViewDrawing->GetDocument());
    if (pDrawingLayoutManager == nullptr)
    {
        return false;
    }

    gcmp::IDrawingLayout* pDrawingLayout = nullptr;
    std::vector<gcmp::IDrawingLayout*> pDrawingLayouts;
    for (gcmp::IDrawingLayout* pDrawingLayoutItem : pDrawingLayoutManager->GetAllLayouts())
    {
        pDrawingLayouts.emplace_back(pDrawingLayoutItem);
        if (pDrawingLayoutItem->GetModelView()->GetElementId().AsInt64() == pModelViewDrawing->GetElementId().AsInt64())
        {
            pDrawingLayout = pDrawingLayoutItem;
            break;
        }
    }

    if (nullptr == pDrawingLayout)
    {
        OwnerPtr<IUserTransaction> opUserTransaction = IUserTransaction::Create(pModelViewDrawing->GetDocument(), L"CreateDrawingLayout", true);

        pDrawingLayout = pDrawingLayoutManager->CreateLayout(L"DrawingLayout", nullptr);
        if (nullptr == pDrawingLayout)
        {
            opUserTransaction->Rollback();
            return false;
        }

        opUserTransaction->Commit();
    }

    gcmp::IDrawingViewport* pDrawingViewPort = nullptr;
    for (gcmp::IDrawingViewport* pDrawingViewPortItem : pDrawingLayout->GetAllViewports())
    {
        if (pModelViewDrawing->GetElementId() == pDrawingViewPortItem->GetModelViewId())
        {
            pDrawingViewPort = pDrawingViewPortItem;
            break;
        }
    }

    if (nullptr == pDrawingViewPort)
    {
        OwnerPtr<IUserTransaction> opUserTransaction = IUserTransaction::Create(pModelViewDrawing->GetDocument(), L"CreateViewPort", true);
        pDrawingViewPort = pDrawingLayout->CreateViewport(pModelViewDrawing->GetElementId(), L"DefaultViewPort", Vector3d(0, 0, 0), nullptr);
        if (pDrawingViewPort == nullptr)
        {
            opUserTransaction->Rollback();
            return false;
        }
        pDrawingViewPort->SetFrameVisibility(GraphicsNodeVisibility::Always);
        pDrawingViewPort->SetIsEnableActive(true);
        pDrawingViewPort->SetIsSwitchActiveState(true);
        //pDrawingViewPort->SetAnchorPoint(ViewportAnchorPointType::BottomLeft);

        opUserTransaction->Commit();
    }

    return true;
}

bool SampleUIUtils::CreateUiViewDrawing(gcmp::IUiDocument* pUiDocument, const std::wstring viewName, gcmp::IUiView* &pUiViewDrawing)
{
    pUiViewDrawing = nullptr;
    if (nullptr == pUiDocument)
    {
        return false;
    }

    //1.检查打开的视图是否有图纸视图
    for each (IUiView* pUiView in pUiDocument->GetAllUiViews())
    {
        if (pUiView->IsSubUiView())
        {
            continue;
        }

        if (gcmp::BuiltInViewType::Drawing != pUiView->GetModelView()->GetViewType())
        {
            continue;
        }

        gcmp::IModelView* pModelViewDrawing = pUiView->GetModelView();
        if (!(gcmp::Vector3dUtils::IsEqual(Vector3d(1, 0, 0), pModelViewDrawing->GetRightDirection()) && gcmp::Vector3dUtils::IsEqual(Vector3d(0, 0, 1), pModelViewDrawing->GetUpDirection())))
        {
            //continue;
        }

        if (pUiView->GetTag() == viewName)
        {
            pUiViewDrawing = pUiView;
            break;
        }
    }

    //2.有的话直接打开
    if (pUiViewDrawing != nullptr)
    {
        if (gcmp::UiDocumentViewUtils::GetCurrentUiView()->GetId() != pUiViewDrawing->GetId())
        {
            gcmp::IModelView* pModelView = pUiViewDrawing->GetModelView();
            CheckDrawingLayoutAndViewPort(pUiViewDrawing->GetModelView());
            gcmp::IMainWindow::GetMainWindow()->CreateOrOpenUiView(pUiDocument->GetId(), pUiViewDrawing->GetModelView()->GetElementId(), false, pUiViewDrawing->GetTag());
        }
        else
        {
            pUiViewDrawing->GetCanvas()->Refresh();
        }

        return true;
    }

    //3.没有的话继续检查文档是否有图纸模型
    gcmp::IModelView* pModelViewDrawing = GetOrCreateModelViewDrawing(pUiDocument->GetDbDocument(), L"ModelViewForDrawing");
    if (nullptr == pModelViewDrawing)
    {
        return false;
    }

    pUiViewDrawing = gcmp::IMainWindow::GetMainWindow()->CreateOrOpenUiView(pUiDocument->GetId(), pModelViewDrawing->GetElementId(), false, viewName);
    return (nullptr != pUiViewDrawing);
}

void SampleUIUtils::AddPage(const std::wstring& id, const std::wstring& caption)
{
    IMainWindow* pMainWnd = IMainWindow::GetMainWindow();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pMainWnd, L"获取主窗口失败", L"GDMPLab", L"2024-03-30");
    IRibbon* pRibbon = pMainWnd->GetRibbon();
    OwnerPtr<IRibbonPage> opPageAPI = IRibbonPage::Create(id, caption);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pRibbon, L"获取RibbonBar失败", L"GDMPLab", L"2024-03-30");
    pRibbon->AddPage(TransferOwnership(opPageAPI));
}

namespace
{
    class PageHandler : public gcmp::IPageHandler
    {
    public:
        virtual bool IsVisible() const override
        {
            return true;
        }
        virtual bool IsEnabled() const override
        {
            return true;
        }
    };
}

gcmp::IRibbonGroup* SampleUIUtils::FindOrCreateGroup(const std::wstring& pageId, const std::wstring& groupId, const std::wstring& groupName)
{
    IMainWindow* pMainWnd = IMainWindow::GetMainWindow();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pMainWnd, L"获取主窗口失败", L"GDMPLab", L"2024-03-30");

    IRibbon* pRibbon = pMainWnd->GetRibbon();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pRibbon, L"获取RibbonBar失败", L"GDMPLab", L"2024-03-30");
    //建模
    IRibbonPage* pPage = pRibbon->GetPage(pageId);

    if (!pPage)
    {
        if (pageId == ID_PAGE_FALCONV)
        {
            // FalconV插件示例
            OwnerPtr<IRibbonPage> opPageModule = IRibbonPage::Create(ID_PAGE_FALCONV, GBMP_TR(L"FalconV"));
            opPageModule->SetPageHandler(TransferOwnership(NEW_AS_OWNER_PTR(PageHandler)));
            pPage = opPageModule.get();
            pRibbon->AddPage(TransferOwnership(opPageModule));
        }
    }

    return GetGroup(pageId, groupId, groupName);
}

gcmp::IRibbonGroup* SampleUIUtils::GetGroup(const std::wstring& pageId, const std::wstring& groupId, const std::wstring& groupName)
{
    IMainWindow* pMainWnd = IMainWindow::GetMainWindow();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pMainWnd, L"获取主窗口失败", L"GDMPLab", L"2024-03-30");

    IRibbon* pRibbon = pMainWnd->GetRibbon();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pRibbon, L"获取RibbonBar失败", L"GDMPLab", L"2024-03-30");
    //建模
    IRibbonPage* pPage = pRibbon->GetPage(pageId);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pPage, L"获取建模标签页失败", L"GDMPLab", L"2024-03-30");

    IRibbonGroup* pGroup = pPage->GetGroup(groupId);
    if (pGroup)
    {
        return pGroup;
    }

    OwnerPtr<IRibbonGroup> opGroup = IRibbonGroup::Create(groupId, groupName);
    if (pPage->AddGroup(TransferOwnership(opGroup)))
    {
        return pPage->GetGroup(groupId);
    }
    return nullptr;
}

gcmp::IRibbonGroup* SampleUIUtils::FindGroup(const std::wstring& pageId, const std::wstring& groupId)
{
    IMainWindow* pMainWnd = IMainWindow::GetMainWindow();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pMainWnd, L"获取主窗口失败", L"GDMPLab", L"2024-03-30");

    IRibbon* pRibbon = pMainWnd->GetRibbon();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pRibbon, L"获取RibbonBar失败", L"GDMPLab", L"2024-03-30");
    //建模
    IRibbonPage* pPage = pRibbon->GetPage(pageId);

    if (!pPage)
    {
        return nullptr;
    }
    return pPage->GetGroup(groupId);
}

bool SampleUIUtils::LoadButtons(gcmp::IRibbonGroup* pGroup, const std::vector<AddinButton>& btns)
{
    if (!pGroup)
    {
        return false;
    }
    for (auto&& obj : btns)
    {
        if (obj.subs.empty())
        {
            OwnerPtr<ICommandButtonDefinition> opItem =
                ICommandButtonDefinition::Create(obj.id, GBMP_TR(obj.title).c_str(), GBMP_TR(obj.title).c_str(), obj.id, obj.icon);
            std::vector<IControl*> pControls = pGroup->GetAllControls();
            if (obj.id != ID_CMD_CLOSE_MODULE && pGroup->GetControl(ID_CMD_CLOSE_MODULE))
            {
                pGroup->InsertButton(ID_CMD_CLOSE_MODULE, TransferOwnership(opItem));
            }
            else
            {
                pGroup->AddButton(TransferOwnership(opItem));
            }
        }
        else
        {
            OwnerPtr<ICommandButtonMenuDefinition> opItem =
                ICommandButtonMenuDefinition::Create(obj.id, obj.subs[0].id, GBMP_TR(obj.title).c_str(), obj.icon);

            for (auto&& sub : obj.subs)
            {
                OwnerPtr<ICommandMenuItemDefinition> opSub =
                    ICommandMenuItemDefinition::Create(sub.id, sub.id, GBMP_TR(sub.title).c_str(), GBMP_TR(sub.title).c_str(), sub.icon);
                opItem->AddItem(TransferOwnership(opSub));
            }
            pGroup->AddButtonMenu(TransferOwnership(opItem));
        }
    }
    return true;
}

class PickCheckBoxGroupHandler : public ICheckBoxGroupHandler
{
public:
    virtual bool IsVisible(const std::wstring& id) const override
    {
        return GcmpCommandState::IsInDocumentEnvironment();
    }

    virtual bool IsEnabled(const std::wstring& id) const override
    {
        return GcmpCommandState::IsInDocumentEnvironment();
    }

    virtual void On(ICheckBoxGroupEventArgs* pArgs) override
    {
    }
};

OwnerPtr<ICheckBoxGroupDefinition> SampleUIUtils::CreatePickCheckBoxGroup(std::wstring groupId, bool onlyBasic)
{
    OwnerPtr<ICheckBoxGroupDefinition> opCheckBoxGroup = ICheckBoxGroupDefinition::Create(groupId + ID_SAMPLE_PICKER_FILTER_GROUP, ID_SAMPLE_PICKER_FILTER_GROUP);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableFace, L"面", L"Face", CheckState::Checked);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableEdge, L"边", L"Edge", CheckState::Checked);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnablePoint, L"点", L"SubNodeOfZoomFree", CheckState::Checked);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableCurve, L"曲线", L"Curve", CheckState::Checked);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableText, L"文字", L"Text", CheckState::Checked);
    opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableMeshTriangle, L"Mesh", L"MeshTriangle", CheckState::Checked);
    if (!onlyBasic)
    {
        opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableSegmentOfPolyCurve3d, L"多段线的线", L"SegmentOfPolyCurve3d", CheckState::Checked);
        opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableSegmentOfPolyCurve, L"二维多段线的线", L"SegmentOfPolyCurve", CheckState::Checked);
        opCheckBoxGroup->AddCheckBox(ID_SAMPLE_PICKER_FILTER_EnableGRep, L"图元", L"GraphicsElementShape", CheckState::Checked);
    }
    opCheckBoxGroup->SetControlHandler(TransferOwnership(NEW_AS_OWNER_PTR(PickCheckBoxGroupHandler)));

    return TransferOwnership(opCheckBoxGroup);
}

const bool Sample::SampleUIUtils::GetCheckboxValue(IRibbonGroup* pRibbonGroup, std::wstring checkboxId)
{
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pRibbonGroup, L"pRibbonGroup为空", L"GDMPLab", L"2024-03-30");
    IControl* pControl = pRibbonGroup->GetControl(pRibbonGroup->GetGroupId() + ID_SAMPLE_PICKER_FILTER_GROUP);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pControl, L"pControl为空", L"GDMPLab", L"2024-03-30");
    ICheckBoxGroupDefinition* pCheckBoxGroupDefinition = dynamic_cast<ICheckBoxGroupDefinition*>(pControl->GetControlDefinition());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pCheckBoxGroupDefinition, L"pCheckBoxGroupDefinition为空", L"GDMPLab", L"2024-03-30");
    bool ok;
    return pCheckBoxGroupDefinition->GetCheckState(checkboxId, &ok) == CheckState::Checked;
}

void Sample::SampleUIUtils::SetPickTarget(IRibbonGroup* pRibbonGroup, gcmp::IPickTarget * pPickTarget)
{
    pPickTarget->DisableAll();

    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableFace)) { pPickTarget->EnableFace(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableEdge)) { pPickTarget->EnableEdge(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableCurve)) { pPickTarget->EnableCurve(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableText)) { pPickTarget->EnableText(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableMeshTriangle)) { pPickTarget->EnableMeshTriangle(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnablePoint)) { pPickTarget->EnablePoint(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableSegmentOfPolyCurve3d)) { pPickTarget->EnableSegmentOfPolyCurve3d(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableSegmentOfPolyCurve)) { pPickTarget->DisableSegmentOfPolyCurve(); };
    if (GetCheckboxValue(pRibbonGroup, ID_SAMPLE_PICKER_FILTER_EnableGRep)) { pPickTarget->EnableGraphicsElementShape(); };
}

gcmp::IControlDefinition* Sample::SampleUIUtils::FindControlDefinition(IRibbonGroup* pGroup, const std::wstring& id)
{
    IControl* pControl = pGroup->GetControl(id);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pControl, id + L"未找到", L"GDMPLab", L"2024-03-30");
    return pControl->GetControlDefinition();
}

bool Sample::SampleUIUtils::OutputMessage(const std::wstring& str)
{
    HMODULE hModule = ::GetModuleHandle(L"GdCADWrapper.dll");
    if (NULL == hModule)
    {
        return false;
    }
    typedef bool(*GdPrint)(const wchar_t*);
    GdPrint func = (GdPrint)GetProcAddress(hModule, "GdPrint");
    if (NULL == func)
    {
        return false;
    }
    return func(str.c_str());
}

void ModuleUIUtils::SetupModuleUI(gcmp::OwnerPtr<gcmp::IRibbonGroup> opRibbonGroup, const std::wstring& contextualPageCaption)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(opRibbonGroup, L"opRibbonGroup为空", L"GDMPLab", L"2024-03-30");

    IMainWindow* pMainWindow = SampleUIUtils::GetMainWindow();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pMainWindow, L"pMainWindow为空", L"GDMPLab", L"2024-03-30");

    IRibbon* pRibbon = pMainWindow->GetRibbon();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pRibbon, L"pRibbon为空", L"GDMPLab", L"2024-03-30");

    IRibbonPage* pRibbonPage = pRibbon->GetPage(ID_PAGE_MODULE);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pRibbonPage, L"pRibbonPage为空", L"GDMPLab", L"2024-03-30");
    bool flag = pRibbonPage->AddGroup(TransferOwnership(opRibbonGroup));
    DBG_WARN_AND_RETURN_VOID_UNLESS(flag, L"AddGroup失败", L"GDMPLab", L"2024-03-30");
}

void ModuleUIUtils::RemoveModuleUI()
{
    SampleGroupHandler::MakeVisible(L"");
}

ModuleUIUtils* ModuleUIUtils::Get()
{
    static ModuleUIUtils moduleUIUtils;
    return &moduleUIUtils;
}

#pragma  region CmdHelpBase
CmdHelpBase::CmdHelpBase(std::wstring commandId, std::wstring helpUrl)
    : CommandBase(commandId), m_helpUrl(helpUrl) {}
CmdHelpBase::~CmdHelpBase() {}

gcmp::OwnerPtr<gcmp::IAction> CmdHelpBase::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    ShellExecute(NULL, L"open", m_helpUrl.c_str(), NULL, NULL, SW_SHOWNORMAL);
    return nullptr;
}

bool CmdHelpBase::IsEnabled() const
{
    return GcmpCommandState::IsInDocumentEnvironment();
}

bool CmdHelpBase::IsVisible() const
{
    return GcmpCommandState::IsInDocumentEnvironment();
}
#pragma endregion
