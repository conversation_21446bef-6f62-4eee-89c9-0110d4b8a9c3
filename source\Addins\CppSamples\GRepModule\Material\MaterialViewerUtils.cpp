﻿#include "MaterialViewerUtils.h"
#include "IUiView.h"
#include "IElement.h"
#include "IDocument.h"
#include "ElementId.h"
#include "ICategory.h"
#include "IModelView.h"
#include "IUiDocument.h"
#include "UniIdentity.h"
#include "IDirectShape.h"
#include "IGraphicsNode.h"
#include "IGenericElement.h"
#include "ICategoryLibrary.h"
#include "IElementModelShape.h"
#include "UiDocumentViewUtils.h"
#include "IGraphicsElementShape.h"
#include "IGraphicsNodeReference.h"
#include "IElementBasicInformation.h"
#include "IGraphicsMaterialManager.h"
#include "IGraphicsStyleDataOverrideItems.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "IGraphicsNodeStyleAndMaterialOverride.h"

#include "GRepModule.h"
#include "GRepCommandIds.h"
#include "CommandRegister.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h" 

using namespace gcmp;
using namespace Sample;

#pragma region 初始化和主要的获取设置材质数据入口

MaterialViewerUtils::MaterialViewerUtils(IUiView* pCurrentView, ElementId elemId, const IGraphicsNode* gNode)
    : m_pUiView(pCurrentView)
    , m_elemId(elemId)
    , m_pGNode(gNode)
{
    IUiDocument* pUIDoc = pCurrentView->GetUiDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pUIDoc, L"pUIDoc为空",L"GDMPLab",L"2024-09-30");
    m_pDocument = pUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(m_pDocument, L"pDoc为空",L"GDMPLab",L"2024-09-30");
    DBG_WARN_AND_RETURN_VOID_UNLESS(GetElement(), L"GetElement()为空",L"GDMPLab",L"2024-09-30");
    const IElementBasicInformation* pElemBasicInfo = GetElement()->GetBasicInformation();
    m_categoryId = pElemBasicInfo->GetCategoryUid();
}

std::vector<OwnerPtr<MaterialOverrideData>> MaterialViewerUtils::GetMaterials(bool canGNodeSetDirectly)
{
    std::vector<OwnerPtr<MaterialOverrideData>> gOverrideDataList;
    
    // 类别设置为不可以设置材质是业务逻辑，实际上也可以设置
    //if (!CanCategoryHaveGraphicsMaterial(m_pDocument, m_categoryId))
    //{
    //    for (int i = 0; i < 9; i++)
    //    {
    //        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
    //        gOverrideDataList.emplace_back(TransferOwnership(opOverrideData));
    //    }
    //    return gOverrideDataList;
    //}

    IModelView* pModelView = m_pUiView->GetModelView();
    DBG_WARN_AND_RETURN_UNLESS(pModelView, gOverrideDataList, L"pModelView为空",L"GDMPLab",L"2024-09-30");
    IGraphicsMaterialManager* pGMaterialMgr = m_pDocument->GetGraphicsMaterialManager();
    DBG_WARN_AND_RETURN_UNLESS(pGMaterialMgr, gOverrideDataList, L"pGMaterialMgr 为空",L"GDMPLab",L"2024-09-30");

    // 文档类别级（IGraphicsStyleManager::GetGraphicsStyleIdByCategoryUid）
    {
        ElementId docCategoryMaterialId = pGMaterialMgr->GetGraphicsMaterialIdByCategoryUid(m_categoryId);
        IGraphicsMaterial* docCategoryMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(docCategoryMaterialId));
        DBG_WARN_AND_RETURN_UNLESS(docCategoryMaterial, gOverrideDataList, L"pMaterial为空",L"GDMPLab",L"2024-09-30");
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        opOverrideData->m_available = true;
        if (docCategoryMaterial->IsBoundWithMaterialInstance())
        {
            opOverrideData->m_materialId = docCategoryMaterialId;
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 视图类别级（IModelView::GetGraphicsStyleOverrideByCategoryUid）
    {
        std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> overridePair =
            pModelView->GetGraphicsStyleOverrideByCategoryUid(m_categoryId);
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        opOverrideData->m_available = false;
        OwnerPtr<IGraphicsMaterialData> opMaterialData = pModelView->GetGraphicsMaterialOverrideByCategoryUid(m_categoryId);
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 文档级图元
    //（IElement::GetShapeGraphicsNodeStyleAndMaterialOverrideComponent）
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
        const IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent();
        if (pGNodeStyleOverride)
        {
            opOverrideData->m_available = true;
            ElementId materialId = pGNodeStyleOverride->GetOverriddenGraphicsMaterial();
            opOverrideData->m_materialId = materialId;
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 视图级图元
    //（IElement::GetShapeGraphicsNodeStyleAndMaterialOverrideComponent）
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        opOverrideData->m_available = true;
        opOverrideData->m_materialId = pModelView->GetOverridedGraphicsMaterialByElementId(m_elemId);
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 文档级节点：按材质名称
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            if (gNodeMaterial)
            {
                //! 注意用材质名称重载用的是IElementBasicInformation的名字
                const IElementBasicInformation* pBasicInfo = gNodeMaterial->GetBasicInformation();
                std::wstring materialBaicInfoName = pBasicInfo->GetName();
                // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
                const IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent();
                if (pGNodeStyleOverride)
                {
                    opOverrideData->m_available = true;
                    opOverrideData->m_hasGNodeText = true;
                    opOverrideData->m_gNodeText = materialBaicInfoName;
                    const std::map<std::wstring, ElementId>& materialNameMatMap = pGNodeStyleOverride->GetOverriddenGraphicsMaterialsByMaterialName();
                    auto materialIdIter = materialNameMatMap.find(materialBaicInfoName);
                    // 找到了重载
                    if (materialIdIter != materialNameMatMap.end())
                    {
                        opOverrideData->m_materialId = materialIdIter->second;
                    }
                }
            }
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 文档级节点：按材质类别
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            if (gNodeMaterial)
            {
                UniIdentity categoryId = gNodeMaterial->GetTargetCategoryUid();
                // 拾取GNode的材质有类别
                if (categoryId.IsValid())
                {
                    const ICategory* pCategory = ICategoryLibrary::Get(m_pDocument)->GetCategory(categoryId);
                    // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
                    const IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent();
                    if (pGNodeStyleOverride)
                    {
                        opOverrideData->m_available = true;
                        opOverrideData->m_hasGNodeMaterialCategory = true;
                        opOverrideData->m_gNodeCategory = pCategory;
                        const std::map<UniIdentity, ElementId>& materialNameMatMap = pGNodeStyleOverride->GetOverriddenGraphicsMaterialsByTargetCategoryUid();
                        auto materialIdIter = materialNameMatMap.find(categoryId);
                        // 找到了重载
                        if (materialIdIter != materialNameMatMap.end())
                        {
                            opOverrideData->m_materialId = materialIdIter->second;
                        }
                    }
                }
            }
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 视图级节点：按材质名称
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            if (gNodeMaterial)
            {
                //! 注意用材质名称重载用的是IElementBasicInformation的名字
                const IElementBasicInformation* pBasicInfo = gNodeMaterial->GetBasicInformation();
                std::wstring materialBaicInfoName = pBasicInfo->GetName();

                opOverrideData->m_available = true;
                opOverrideData->m_hasGNodeText = true;
                opOverrideData->m_gNodeText = materialBaicInfoName;
                const std::map<std::wstring, ElementId>& materialNameMatMap = pModelView->GetOverriddenElementGraphicsMaterialByMaterialName(m_elemId);
                auto materialIdIter = materialNameMatMap.find(materialBaicInfoName);
                // 找到了重载
                if (materialIdIter != materialNameMatMap.end())
                {
                    opOverrideData->m_materialId = materialIdIter->second;
                }
            }
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 视图级节点：按材质类别
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            if (gNodeMaterial)
            {
                UniIdentity categoryId = gNodeMaterial->GetTargetCategoryUid();
                const ICategory* pCategory = ICategoryLibrary::Get(m_pDocument)->GetCategory(categoryId);
                // 拾取GNode的材质有类别
                if (categoryId.IsValid())
                {
                    opOverrideData->m_available = true;
                    opOverrideData->m_hasGNodeMaterialCategory = true;
                    opOverrideData->m_gNodeCategory = pCategory;
                    const std::map<UniIdentity, ElementId>& materialNameMatMap = pModelView->GetOverriddenElementGraphicsMaterialByTargetCategoryUid(m_elemId);
                    auto materialIdIter = materialNameMatMap.find(categoryId);
                    // 找到了重载
                    if (materialIdIter != materialNameMatMap.end())
                    {
                        opOverrideData->m_materialId = materialIdIter->second;
                    }
                }
            }
        }
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    // 节点直接设置
    {
        OwnerPtr<MaterialOverrideData> opOverrideData = NEW_AS_OWNER_PTR(MaterialOverrideData);
        ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
        IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
        opOverrideData->m_available = canGNodeSetDirectly;
        // 如果节点有材质
        if (gNodeMaterial)
        {
            opOverrideData->m_materialId = gNodeMaterialId;
            //! 注意用材质名称重载用的是IElementBasicInformation的名字
            const IElementBasicInformation* pBasicInfo = gNodeMaterial->GetBasicInformation();
            std::wstring materialBaicInfoName = pBasicInfo->GetName();
            opOverrideData->m_gNodeText = materialBaicInfoName;
            UniIdentity categoryId = gNodeMaterial->GetTargetCategoryUid();
            // 拾取GNode的材质有类别
            if (categoryId.IsValid())
            {
                opOverrideData->m_hasGNodeMaterialCategory = true;
                ICategoryLibrary* pCategoryLib = ICategoryLibrary::Get(m_pDocument);
                const ICategory* pCategory = pCategoryLib->GetCategory(categoryId);
                opOverrideData->m_gNodeCategory = pCategory;
            }
        }
        opOverrideData->m_hasGNodeText = true;
        gOverrideDataList.push_back(TransferOwnership(opOverrideData));
    }
    return TransferOwnership(gOverrideDataList);
}

void MaterialViewerUtils::SetMaterials(std::vector<OwnerPtr<MaterialOverrideData>> gOverrideDataList, bool canGNodeSetDirectly)
{
    IModelView* pModelView = m_pUiView->GetModelView();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pModelView, L"pModelView为空",L"GDMPLab",L"2024-09-30");
    IGraphicsMaterialManager * pMaterialManager = m_pDocument->GetGraphicsMaterialManager();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pMaterialManager, L"pMaterialManager 为空",L"GDMPLab",L"2024-09-30");
    IDocument* pDocument = pModelView->GetDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDocument, L"pDocument 为空",L"GDMPLab",L"2024-09-30");

    ElementId elemId = GetElement()->GetElementId();
    ElementId viewId = pModelView->GetElementId();
    int index = 0;
    // 文档类别级：IGraphicsStyleManager::GetGraphicsStyleIdByCategoryUid
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opOverrideData, L"opOverrideData为空",L"GDMPLab",L"2024-09-30");
        ElementId categoryMaterialId = pMaterialManager->GetGraphicsMaterialIdByCategoryUid(m_categoryId);
        IGraphicsMaterial* categoryMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(categoryMaterialId));
        if (categoryMaterial && categoryMaterial->GetElementId() != opOverrideData->m_materialId)
        {
            categoryMaterial->UnbindMaterialInstance();
            if (opOverrideData->m_materialId.IsValid())
            {
                categoryMaterial->BindMaterialInstance(opOverrideData->m_materialId);
            }
        }
    }
    // 视图类别级：IModelView::OverrideGraphicsStyleByCategoryUid
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opOverrideData, L"opOverrideData为空",L"GDMPLab",L"2024-09-30");
        // pModelView->OverrideGraphicsMaterialByCategoryUid(m_categoryId, );
    }
    // 文档级图元
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
        IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponentFW();
        if (opOverrideData->m_available && pGNodeStyleOverride)
        {
            pGNodeStyleOverride->OverrideGraphicsMaterial(opOverrideData->m_materialId);
        }
    }
    // 视图级图元
    // SampleGraphicsNodeStyleAndMaterialCustomizer不能存储数据；样式重载数据都存在CustomizerGStyleManager，因此直接修改CustomizerGStyleManager
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opOverrideData, L"opOverrideData为空",L"GDMPLab",L"2024-09-30");
        pModelView->OverrideGraphicsMaterialByElementId(m_elemId, opOverrideData->m_materialId);
    }
    // 文档级节点：按材质名称
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            bool isValidMaterial = IsValidMaterial(gNodeMaterial);
            if (isValidMaterial)
            {
                //! 注意用材质名称重载用的是IElementBasicInformation的名字
                const IElementBasicInformation* pBasicInfo = gNodeMaterial->GetBasicInformation();
                std::wstring materialBaicInfoName = pBasicInfo->GetName();
                // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
                IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponentFW();
                if (pGNodeStyleOverride)
                {
                    pGNodeStyleOverride->OverrideGraphicsMaterialByMaterialName(materialBaicInfoName, opOverrideData->m_materialId);
                }
            }
        }
    }
    // 文档级节点：按材质类别
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            bool isValidMaterial = IsValidMaterial(gNodeMaterial);
            if (isValidMaterial)
            {
                UniIdentity categoryId = gNodeMaterial->GetTargetCategoryUid();
                // 拾取GNode的材质有类别
                if (categoryId.IsValid())
                {
                    // 不是所有图元都用IGraphicsNodeStyleAndMaterialOverride
                    const IGraphicsNodeStyleAndMaterialOverride* pGNodeStyleOverride = GetElement()->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent();
                    if (pGNodeStyleOverride)
                    {
                        pGNodeStyleOverride->OverrideGraphicsMaterialByTargetCategoryUid(categoryId, opOverrideData->m_materialId);
                    }
                }
            }
        }
    }
    // 视图级节点：按材质名称
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            bool isValidMaterial = IsValidMaterial(gNodeMaterial);
            if (isValidMaterial)
            {
                //! 注意用材质名称重载用的是IElementBasicInformation的名字
                const IElementBasicInformation* pBasicInfo = gNodeMaterial->GetBasicInformation();
                std::wstring materialBaicInfoName = pBasicInfo->GetName();
                pModelView->OverrideElementGraphicsMaterialByMaterialName(m_elemId, materialBaicInfoName, opOverrideData->m_materialId);
            }
        }
    }
    // 视图级节点：按材质类别
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        // 只有拾取GNode时
        if (m_pGNode)
        {
            ElementId gNodeMaterialId = m_pGNode->GetGraphicsMaterialId();
            IGraphicsMaterial* gNodeMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(gNodeMaterialId));
            // 拾取的GNode有材质
            bool isValidMaterial = IsValidMaterial(gNodeMaterial);
            if (isValidMaterial)
            {
                UniIdentity categoryId = gNodeMaterial->GetTargetCategoryUid();
                // 拾取GNode的材质有类别
                if (categoryId.IsValid())
                {
                    pModelView->OverrideElementGraphicsMaterialByTargetCategoryUid(m_elemId, categoryId, opOverrideData->m_materialId);
                }
            }
        }
    }
    // 节点直接设置
    index++;
    {
        OwnerPtr<MaterialOverrideData>& opOverrideData = gOverrideDataList.at(index);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opOverrideData, L"opOverrideData为空",L"GDMPLab",L"2024-09-30");
        IGraphicsMaterial* pGMaterial = quick_cast<IGraphicsMaterial>(m_pDocument->GetElement(opOverrideData->m_materialId));
        bool isValidMaterial = IsValidMaterial(pGMaterial);
        if(isValidMaterial)
        {
            IElementBasicInformation* pElemBasicInfo = pGMaterial->GetBasicInformation();
            pElemBasicInfo->SetName(opOverrideData->m_gNodeText);
            std::wstring test = pElemBasicInfo->GetName();
        }
        else
        {

        }
        // 如果有有效性设置，或者原本节点就有样式；并且可以直接设置图形节点的显示样式（例如GRep探针浏览器中的图形节点是复制缓存的，只有特定图元可以设置）
        if (canGNodeSetDirectly && m_pGNode)
        {
            // 暂时通过hack的方式强行设置。对于有些图元和有些设置，这样会无效；会被图元自身计算器的设置覆盖
            IGraphicsNode* pGNode = const_cast<IGraphicsNode*>(m_pGNode);
            if (isValidMaterial)
            {
                pGNode->SetGraphicsMaterialId(opOverrideData->m_materialId);
            }
            else
            {
                pGNode->SetGraphicsMaterialId(ElementId::InvalidID);
            }
        }
    }
}
#pragma endregion

#pragma region 辅助函数
IElement * Sample::MaterialViewerUtils::GetElement()
{
    return m_pDocument->GetElement(m_elemId);
}

bool Sample::MaterialViewerUtils::IsValidMaterial(const gcmp::IGraphicsMaterial * pGMaterial)
{
    if (nullptr == pGMaterial)
    {
        return false;
    }
    UniIdentity categoryId = pGMaterial->GetTargetCategoryUid();
    if (categoryId.IsValid())
    {
        return pGMaterial->IsBoundWithMaterialInstance();
    }
    return true;
}

bool MaterialViewerUtils::CanCategoryHaveGraphicsMaterial(const IDocument* pDocument, const UniIdentity& categoryUid) const
{
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pDocument, L"pDocument为空",L"GDMPLab",L"2024-09-30");

    // 得到类别
    const ICategoryLibrary* pCategoryLib = ICategoryLibrary::Get(const_cast<IDocument*>(pDocument));
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pCategoryLib, L"pCategoryLib为空",L"GDMPLab",L"2024-09-30");

    const ICategory* pCategory = pCategoryLib->GetCategory(categoryUid);
    if (pCategory == nullptr)
        return false;

    // 从类别中获取是否允许设置材质
    return pCategory->CanHaveGraphicsMaterial();
}
#pragma endregion


