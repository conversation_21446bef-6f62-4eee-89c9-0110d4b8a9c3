#include "GraphicsStyleTable.h"
#include "IUiView.h"
#include "ILine3d.h"
#include "FilePath.h"
#include "IElement.h"
#include "ICategory.h"
#include "IDocument.h"
#include "IModelView.h"
#include "IUiDocument.h"
#include "FileUtility.h"
#include "UniIdentity.h"
#include "JournalUtils.h"
#include "IFillPattern.h"
#include "ILineTypeNew.h"
#include "ILineTypeData.h"
#include "UiCommonDialog.h"
#include "IPublishConfig.h"
#include "IGraphicsStyle.h"
#include "GbmpFileSystem.h"
#include "IGenericElement.h"
#include "ICategoryLibrary.h"
#include "IFillPatternData.h"
#include "IUserTransaction.h"
#include "IGraphicsMaterial.h"
#include "IFaceHatchPattern.h"
#include "CommandParameters.h"
#include "IGraphicsStyleData.h"
#include "IJournalCommandData.h"
#include "IGraphicsMaterialData.h"
#include "IGraphicsStyleManager.h"
#include "IUiDocumentViewManager.h"
#include "IGraphicsMaterialManager.h"
#include "IElementBasicInformation.h"
#include "IGraphicsStyleDataOverrideItems.h"
#include "GcmpBuiltInCategoryUniIdentities.h"

#include "QLabel"
#include "QObject"
#include "QWidget"
#include <QPainter>
#include <QComboBox>
#include "QCheckBox"
#include "QTableView"
#include <qlineedit.h>
#include "QPushButton"
#include "QHeaderView"
#include <QColorDialog>
#include "QTableWidget"
#include "LineTypeDlg.h"
#include "QItemDelegate"
#include "GStyleViewerUtils.h"
#include "CustomizerGStyleManager.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

/// 本文件是界面实现的辅助类，仅包含非常少的GDMP API的演示内容

using namespace gcmp;
using namespace Sample;

#define MORE_MATERIAL GBMP_TR(L"更多")
#define NO_MATERIAL GBMP_TR(L"无")

namespace
{
    // 用于合成UV标示值
    const int buttonOffset = 1000;
    int MakeIndex(int column, int row)
    {
        return row + buttonOffset * column;
    }

    std::tuple<int, int> GetIndex(int index)
    {
        int column = index / buttonOffset;
        int row = index % buttonOffset;
        return {column, row};
    }
}

namespace Sample
{
#pragma region 辅助类
    static QString STR(std::wstring str)
    {
        return QString::fromStdWString(str);
    }

    static QString STR(std::string str)
    {
        return QString::fromStdString(str);
    }
#pragma endregion

#pragma region cell显示读取数据函数
    void GraphicsStyleTable::MakeCellDisable(int row, int column)
    {
        QLabel *label = new QLabel("", this);
        // 设置样式表使 QLabel 看起来像是被禁用  
        label->setStyleSheet("color: gray; background-color: #cccccc;");
        // 将 QLabel 设置为单元格的小部件  
        m_table->setCellWidget(row, column, label);
    }

    bool GraphicsStyleTable::IsAvailable(int row)
    {
        // 改行显示样式是否有内容？
        return m_gOverrideDataList.at(row)->m_available;
    }

    // 如果该Cell的显示样式不可用，则该Cell灰显显示
    bool GraphicsStyleTable::UpdateAvailableStatus(int row, int column)
    {
        if (!IsAvailable(row))
        {
            MakeCellDisable(row, column);
            return false;
        }
        return true;
    }

    void GraphicsStyleTable::ShowOverrideCell(int row, int column, OverrideFunc overrideFunc)
    {
        // 该行显示样式是否可用
        if (!UpdateAvailableStatus(row, column))return;
        // 该Cell显示样式数据的设置是否可以设置
        if (!overrideFunc.m_canOverride)
        {
            return MakeCellDisable(row, column);
        }
        // 显示样式重载的checkbox
        gcmp::OwnerPtr<QCheckBox> opCkBox = NEW_AS_OWNER_PTR(QCheckBox);
        bool flag = overrideFunc.m_getOverrideFunc(row);
        opCkBox->setChecked(flag);
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opCkBox));
    }

    bool GraphicsStyleTable::GetOverrideCell(int row, int column)
    {
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        return toOverride;
    }

    void GraphicsStyleTable::ShowLineWidthCell(GetDoubleFunc func, SetDoubleFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
        QDoubleValidator* pInputValidator = NEW_AS_QT_CHILD(QDoubleValidator, this);
        pInputValidator->setRange(0.0001, 100000000);
        pInputValidator->setDecimals(2);
        opLineEdit->setValidator(pInputValidator);
        double lineWidth = func(row);
        opLineEdit->setText(QString::number(lineWidth));
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = new FunctionHolder<SetDoubleFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetLineWidthCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = (FunctionHolder<SetDoubleFunc>*)qPointer.value<void*>();  // 获取指针
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        bool bval = false;
        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(row, column));
        double lineWidth = lineEdit->text().toDouble(&bval);
        pSetFuncHolder->m_function(row, lineWidth);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowColorCell(GetColorFunc func, SetColorFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QPushButton> opColorButton = NEW_AS_OWNER_PTR(QPushButton);
        const Color& color = func(row);
        int index = MakeIndex(column, row);
        //看是否数据是非重载
        {
            QColor qColor(color.R, color.G, color.B, color.A);
            //存到color map里.
            m_colorMap[UVIndex(index, 2)] = qColor;
            opColorButton->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
        }
        m_opColorBtnGroup->addButton(opColorButton.get(), index);
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opColorButton));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetColorFunc>* pSetFuncHolder = new FunctionHolder<SetColorFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetColorCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column-1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetColorFunc>* pSetFuncHolder = (FunctionHolder<SetColorFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        int index = MakeIndex(column, row);
        if (m_colorMap.find(UVIndex(index, 2)) != m_colorMap.end())
        {
            int r, g, b, a;
            QColor qColor = m_colorMap[UVIndex(index, 2)];
            qColor.getRgb(&r, &g, &b, &a);
            Color color(r, g, b, a);

            pSetFuncHolder->m_function(row, color);
        }
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowLineTypeCell(GetTextFunc func, SetTextFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QPushButton> opLineTypeButton = NEW_AS_OWNER_PTR(QPushButton);
        opLineTypeButton->setText(QString::fromStdWString(func(row)));
        m_opLineTypeBtnGroup->addButton(opLineTypeButton.get(), MakeIndex(column, row));
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineTypeButton));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetTextFunc>* pSetFuncHolder = new FunctionHolder<SetTextFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetLineTypeCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetTextFunc>* pSetFuncHolder = (FunctionHolder<SetTextFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        QAbstractButton* pButton = m_opLineTypeBtnGroup->button(MakeIndex(column, row));
        std::wstring lineTypeName = pButton->text().toStdWString();
        pSetFuncHolder->m_function(row, lineTypeName);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowEnabledCell(GetBoolFunc func, SetBoolFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        bool enable = func(row);
        gcmp::OwnerPtr<QCheckBox> pCheckBox(NEW_AS_OWNER_PTR(QCheckBox));
        pCheckBox->setChecked(enable);
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pCheckBox));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetBoolFunc>* pSetFuncHolder = new FunctionHolder<SetBoolFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetEnabledCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetBoolFunc>* pSetFuncHolder = (FunctionHolder<SetBoolFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        QCheckBox *ckBxValue = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column));
        pSetFuncHolder->m_function(row, ckBxValue->isChecked());
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowHatchPatternCell(GetTextFunc func, SetTextFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        std::wstring pattern = func(row);
        QStringList widthList;

        int i = 0;
        int currentIndex = 0;
        for (auto itor : m_hatchPatternNames)
        {
            widthList << QString::fromStdWString(itor);
            if (itor == pattern)
            {
                currentIndex = i;
            }
            i++;
        }

        gcmp::OwnerPtr<QComboBox> opComBox = NEW_AS_OWNER_PTR(QComboBox);
        opComBox->addItems(widthList);
        {
            opComBox->setCurrentIndex(currentIndex);
        }
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opComBox));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetTextFunc>* pSetFuncHolder = new FunctionHolder<SetTextFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    //设置填充样式
    void GraphicsStyleTable::SetHatchPatternCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetTextFunc>* pSetFuncHolder = (FunctionHolder<SetTextFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);
           
        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(row, column));
        int widthIndex = comBox->currentIndex() + 1;
        if (widthIndex > 0)
        {
            QString qPattern = comBox->currentText();
            std::wstring pattern = qPattern.toStdWString();
            pSetFuncHolder->m_function(row, pattern);
        }
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowRotateCell(GetDoubleFunc func, SetDoubleFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
        QIntValidator* pInputValidator = NEW_AS_QT_CHILD(QIntValidator, this);
        pInputValidator->setRange(-360, 360);
        opLineEdit->setValidator(pInputValidator);
        int idx = static_cast<int>(MathUtils::RadiansToAngle(func(row)));
        opLineEdit->setText(QString::number(idx));
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = new FunctionHolder<SetDoubleFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetRotateCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = (FunctionHolder<SetDoubleFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        bool bval = false;
        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(row, column));
        int angle = lineEdit->text().toInt(&bval);
        double radians = MathUtils::AngleToRadians(angle);
        pSetFuncHolder->m_function(row, radians);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowScaleCell(GetDoubleFunc func, SetDoubleFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
        QDoubleValidator* pInputValidator = NEW_AS_QT_CHILD(QDoubleValidator, this);
        pInputValidator->setRange(0.0001, 100000000);
        pInputValidator->setDecimals(2);
        opLineEdit->setValidator(pInputValidator);
        double idx = func(row);
        opLineEdit->setText(QString::number(idx));
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
        // 显示样式设置回m_gOverrideDataList的回调函数，绑定到控件上
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = new FunctionHolder<SetDoubleFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetScaleCell(int row, int column)
    {
        if (!IsAvailable(row))return;
        // 从控件获得设置m_gOverrideDataList的回调函数类pSetFuncHolder，设置重载bool值和样式重载
        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetDoubleFunc>* pSetFuncHolder = (FunctionHolder<SetDoubleFunc>*)qPointer.value<void*>();  // 获取指针
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        bool bval = false;
        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(row, column));
        double scale = lineEdit->text().toDouble(&bval);
        pSetFuncHolder->m_function(row, scale);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowTextCell(std::wstring text, int row, int column, bool isReadOnly)
    {
        if (!UpdateAvailableStatus(row, column))return;
        gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
        opLineEdit->setText(QString::fromStdWString(text));
        if (isReadOnly)
        {
            opLineEdit->setEnabled(false);
        }
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
    }

    std::wstring GraphicsStyleTable::GetTextCell(int row, int column)
    {
        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(row, column));
        if (lineEdit)
        {
            return lineEdit->text().toStdWString();
        }
        return L"";
    }

    static std::wstring s_emptyCategory = L"没有类别";

    void GraphicsStyleTable::ShowCategoryCell(int row, int column, const ICategory* pCurrentCategory, bool isEnabled)
    {
        // 图形节点上样式的类别
        std::wstring currentCategory = L"";
        if (pCurrentCategory)
        {
            currentCategory = pCurrentCategory->GetDisplayName();
        }
        else
        {
            currentCategory = s_emptyCategory;
        }

        IDocument* pDoc = m_wpUIDoc->GetDbDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"pDoc 为空",L"GDMPLab",L"2024-09-30");
        CustomizerGStyleManager* pGManger = CustomizerGStyleManager::CreateOrGet(pDoc);
        DBG_WARN_AND_RETURN_VOID_UNLESS(pGManger, L"pGManger为空",L"GDMPLab",L"2024-09-30");

        // 示例程序设计为只有CustomizerGStyleManager::GetGStyleCategories()中的类别可以直接设置到图形节点上
        auto cateMap = pGManger->GetGStyleCategories();
        QStringList widthList;
        widthList << QString::fromStdWString(s_emptyCategory);
        int i = 0;
        int currentIndex = -1;
        for (auto itor : cateMap)
        {
            std::wstring categoryName = itor.first;
            widthList << QString::fromStdWString(categoryName);
            if (categoryName == currentCategory)
            {
                currentIndex = i + 1;
            }
            i++;
        }

        if (currentIndex < 0)
        {
            if (currentCategory == s_emptyCategory)
            {
                currentIndex = 0;
            }
            else
            {
                widthList << QString::fromStdWString(currentCategory);
                currentIndex = widthList.size() - 1;
                // 示例程序设计为：只有图形节点上样式的类别在CustomizerGStyleManager::GetGStyleCategories()中时才可以修改
                isEnabled = false;
            }
        }

        gcmp::OwnerPtr<QComboBox> opComBox = NEW_AS_OWNER_PTR(QComboBox);
        opComBox->addItems(widthList);
        {
            opComBox->setCurrentIndex(currentIndex);
        }
        opComBox->setEnabled(isEnabled);
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opComBox));
    }

    //设置图元节点类别
    void GraphicsStyleTable::SetCategoryCell(int row, int column)
    {
        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(row, column));
        if (!comBox || !comBox->isEnabled())
        {
            return;
        }
        if (comBox->currentIndex() >= 0)
        {
            OwnerPtr<GraphicsOverrideData>& opGraphicsOverrideData = m_gOverrideDataList[row];
            DBG_WARN_AND_RETURN_VOID_UNLESS(opGraphicsOverrideData, L"opGraphicsOverrideData 为空",L"GDMPLab",L"2024-09-30");
            QString qCategoryName = comBox->currentText();
            std::wstring categoryName = qCategoryName.toStdWString();
            if (categoryName == s_emptyCategory)
            {
                opGraphicsOverrideData->m_gNodeCategory = nullptr;
                opGraphicsOverrideData->m_gStyleId = ElementId::InvalidID;
            }

            IDocument* pDoc = m_wpUIDoc->GetDbDocument();
            DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"pDoc 为空",L"GDMPLab",L"2024-09-30");
            CustomizerGStyleManager* pGManger = CustomizerGStyleManager::CreateOrGet(pDoc);
            DBG_WARN_AND_RETURN_VOID_UNLESS(pGManger, L"pGManger为空",L"GDMPLab",L"2024-09-30");
            auto cateMap = pGManger->GetGStyleCategories();
            auto& iter = cateMap.find(categoryName);
            if (iter != cateMap.end())
            {
                opGraphicsOverrideData->m_gNodeCategory = iter->second;
                IGraphicsStyleManager* pGStyleManager = pDoc->GetGraphicsStyleManager();
                DBG_WARN_AND_RETURN_VOID_UNLESS(pGStyleManager, L"pGStyleManager为空",L"GDMPLab",L"2024-09-30");
                m_gOverrideDataList[row]->m_gStyleId = pGStyleManager->GetGraphicsStyleIdByCategoryUid(iter->second->GetUniIdentity());
            }
        }
    }
#pragma endregion

#pragma region 表格初始化
    // 表格中显示样式的行数
    const int GraphicsStyleTable::s_numStyleLines = 9;
    // 【投影和截面线样式】表格的列数
    const int GraphicsStyleTable::s_countLinePageColumns = 23;
    // 【投影和截面面样式】表格的列数（投影面23列，截面面25列）
    const int GraphicsStyleTable::s_countFacePageColumns = 25;
    // 【材质】表格的列数
    const int GraphicsStyleTable::s_countMaterialPageColumns = 5;

    GraphicsStyleTable::GraphicsStyleTable(gcmp::IUiDocument* pUIDoc, gcmp::IUiView* currentView,
        QDialog* parent, TableType tableType, std::vector<gcmp::OwnerPtr<GraphicsOverrideData>>& gOverrideDataList,
        std::vector<gcmp::OwnerPtr<MaterialOverrideData>>& gOverrideMaterialDataList)
        : m_parent(parent)
        , m_wpUIDoc(pUIDoc)
        , m_wpCurrentView(currentView)
        , m_tableType(tableType)
        , m_gOverrideDataList(gOverrideDataList)
        ,m_gOverrideMaterialDataList(gOverrideMaterialDataList)
    {
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_gOverrideDataList.size() == s_numStyleLines, L"m_gOverrideDataList个数错误",L"GDMPLab",L"2024-09-30");
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_wpUIDoc, L"m_wpUIDoc为空",L"GDMPLab",L"2024-09-30");
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_wpCurrentView, L"m_wpCurrentView为空",L"GDMPLab",L"2024-09-30");
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_parent, L"m_parent为空",L"GDMPLab",L"2024-09-30");

        m_opColorBtnGroup = NEW_AS_OWNER_PTR(QButtonGroup);
        m_opLineTypeBtnGroup = NEW_AS_OWNER_PTR(QButtonGroup);

        // 文档对象Tab页
        m_table = NEW_AS_QT_CHILD(QTableWidget, m_parent);
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_table, L"m_table为空",L"GDMPLab",L"2024-09-30");
        m_table->setObjectName(QStringLiteral("GraphicsStyleTable"));
        m_table->verticalHeader()->setHidden(true);
        m_table->horizontalHeader()->setStretchLastSection(true);
        QObject::connect(m_table, SIGNAL(cellClicked(int, int)), this, SLOT(OnClickColorCell(int, int)));
        QObject::connect(m_opColorBtnGroup.get(), SIGNAL(buttonClicked(int)), this, SLOT(OnClickColorCell(int)));
        QObject::connect(m_opLineTypeBtnGroup.get(), SIGNAL(buttonClicked(int)), this, SLOT(OnClickLineTypeCell(int)));

        GStyleViewerUtils::GetHatchPatternNames(pUIDoc->GetDbDocument(), m_hatchPatternNames);
        ////////////////////////////////////////////////////////////////////////////////////
        if (m_tableType == TableType::ProjectionLines)
        {
            initProjectionLines();
        }
        else if (m_tableType == TableType::SectionLines)
        {
            initSectionLines();
        }
        else if (m_tableType == TableType::ProjectionFaces)
        {
            initProjectionFaces();
        }
        else if (m_tableType == TableType::SectionFaces)
        {
            initSectionFaces();
        }
        else if (m_tableType == TableType::Material)
        {
            initMaterial();
        }
        else
        {
            DBG_WARN(L"m_tableType超出预期",L"GDMPLab",L"2024-09-30");
        }
    }

    void GraphicsStyleTable::ShowLineWidthModeCell(GetLineWidthModeFunc func, SetLineWidthModeFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QComboBox> opComBox = NEW_AS_OWNER_PTR(QComboBox);
        QStringList modeList;
        modeList << STR(GBMP_TR(L"定像素线")) << STR(GBMP_TR(L"定宽度线"));
        opComBox->addItems(modeList);

        gcmp::LineWidthMode mode = func(row);
        opComBox->setCurrentIndex(static_cast<int>(mode));

        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opComBox));

        FunctionHolder<SetLineWidthModeFunc>* pSetFuncHolder = new FunctionHolder<SetLineWidthModeFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetLineWidthModeCell(int row, int column)
    {
        if (!IsAvailable(row))return;

        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetLineWidthModeFunc>* pSetFuncHolder = (FunctionHolder<SetLineWidthModeFunc>*)qPointer.value<void*>();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(row, column));
        gcmp::LineWidthMode mode = static_cast<gcmp::LineWidthMode>(comBox->currentIndex());
        pSetFuncHolder->m_function(row, mode);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowSmartColorModeCell(GetSmartColorModeFunc func, SetSmartColorModeFunc setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QComboBox> opComBox = NEW_AS_OWNER_PTR(QComboBox);
        QStringList modeList;
        modeList << STR(GBMP_TR(L"不使用智能颜色")) << STR(GBMP_TR(L"使用背景色")) << STR(GBMP_TR(L"使用背景色的反色"));
        opComBox->addItems(modeList);

        gcmp::SmartColorMode mode = func(row);
        opComBox->setCurrentIndex(static_cast<int>(mode));

        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opComBox));

        FunctionHolder<SetSmartColorModeFunc>* pSetFuncHolder = new FunctionHolder<SetSmartColorModeFunc>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetSmartColorModeCell(int row, int column)
    {
        if (!IsAvailable(row))return;

        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetSmartColorModeFunc>* pSetFuncHolder = (FunctionHolder<SetSmartColorModeFunc>*)qPointer.value<void*>();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(row, column));
        gcmp::SmartColorMode mode = static_cast<gcmp::SmartColorMode>(comBox->currentIndex());
        pSetFuncHolder->m_function(row, mode);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowColorIndexCell(GetInt16Func func, SetInt16Func setFunc, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
        QIntValidator* pInputValidator = NEW_AS_QT_CHILD(QIntValidator, this);
        pInputValidator->setRange(0, 255);
        opLineEdit->setValidator(pInputValidator);
        int16_t colorIndex = func(row);
        opLineEdit->setText(QString::number(colorIndex));
        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));

        FunctionHolder<SetInt16Func>* pSetFuncHolder = new FunctionHolder<SetInt16Func>(setFunc, overrideFunc);
        QVariant qPointer = QVariant::fromValue((void *)pSetFuncHolder);
        m_setFuncDict[UVIndex(row, column)] = qPointer;
    }

    void GraphicsStyleTable::SetColorIndexCell(int row, int column)
    {
        if (!IsAvailable(row))return;

        QCheckBox *ckBx = dynamic_cast<QCheckBox*>(m_table->cellWidget(row, column - 1));
        bool toOverride = (ckBx == nullptr) || ckBx->isChecked();
        QVariant qPointer = m_setFuncDict[UVIndex(row, column)];
        FunctionHolder<SetInt16Func>* pSetFuncHolder = (FunctionHolder<SetInt16Func>*)qPointer.value<void*>();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pSetFuncHolder, L"pSetFuncHolder 为空",L"GDMPLab",L"2024-09-30");
        pSetFuncHolder->m_overrideFunc.m_setOverrideFunc(row, toOverride);

        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(row, column));
        int16_t colorIndex = static_cast<int16_t>(lineEdit->text().toInt());
        pSetFuncHolder->m_function(row, colorIndex);
        delete pSetFuncHolder;
    }

    void GraphicsStyleTable::ShowFinalColorCell(GetFinalColorFunc func, OverrideFunc overrideFunc, int row, int column)
    {
        ShowOverrideCell(row, column, overrideFunc);
        column++;
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::Color finalColor = func(row);
        gcmp::OwnerPtr<QPushButton> opColorButton = NEW_AS_OWNER_PTR(QPushButton);
        opColorButton->setEnabled(false); // 只读显示

        QColor qColor(finalColor.GetRed(), finalColor.GetGreen(), finalColor.GetBlue(), finalColor.A);
        QString colorStyle = QString("background-color: %1; border: 1px solid black;").arg(qColor.name());
        opColorButton->setStyleSheet(colorStyle);
        opColorButton->setText(qColor.name());

        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opColorButton));
    }

    void GraphicsStyleTable::ShowFinalColorCellWithoutCheckbox(GetFinalColorFunc func, int row, int column)
    {
        if (!UpdateAvailableStatus(row, column))return;

        gcmp::Color finalColor = func(row);
        gcmp::OwnerPtr<QPushButton> opColorButton = NEW_AS_OWNER_PTR(QPushButton);
        opColorButton->setEnabled(false); // 只读显示

        QColor qColor(finalColor.GetRed(), finalColor.GetGreen(), finalColor.GetBlue(), finalColor.A);
        QString colorStyle = QString("background-color: %1; border: 1px solid black;").arg(qColor.name());
        opColorButton->setStyleSheet(colorStyle);
        opColorButton->setText(qColor.name());

        m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opColorButton));
    }

    void GraphicsStyleTable::initProjectionLines()
    {
        // 设置投影线样式表头
        const int s_countProjectionLineColumns = 25; // 投影线相关列数（增加了更多属性）
        m_table->setColumnCount(s_countProjectionLineColumns);
        m_table->setRowCount(s_numStyleLines);

        // 分别设置是否重载和重载项两列的宽度
        for (int i = 0; i < s_countProjectionLineColumns - 1; i++)
        {
            if (i % 2 == 0) {
                m_table->setColumnWidth(i, 80);
            }
            else {
                m_table->setColumnWidth(i, 35);
            }
        }
        // 设置第一列和最后两列的宽度
        m_table->setColumnWidth(0, 130);
        m_table->setColumnWidth(23, 110);
        m_table->setColumnWidth(24, 80);

        {
            QStringList header;
            header << STR(GBMP_TR(L"样式重载名称")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"线宽"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"型"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"缩放")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"打印线宽"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"Alpha")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"透明度"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"宽模式")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色索引"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"智能颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"最终颜色"))
                << STR(GBMP_TR(L"节点样式名称")) << STR(GBMP_TR(L"节点样式类别"));
            m_table->setHorizontalHeaderLabels(header);
        }

        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();

        // 类别级显示样式，没有是否可以重载的bool设置
        GetBoolFunc dummyGetFunc = [&](int row) { return false; };
        SetBoolFunc dummySetFunc = [&](int row, bool v) {};
        OverrideFunc dummyOverrideFunc(dummyGetFunc, dummySetFunc, false);

        // 是否可以重载的获得和设置回调函数
        OverrideFuncListList overrideFuncListList;
        {
            OverrideFuncList overrideDummyFuncList;
            for (int i = 0; i < s_countProjectionLineColumns; i++) { overrideDummyFuncList.push_back(dummyOverrideFunc); };

            OverrideFuncList overrideItemsFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLineWidthOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLineWidth(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLineColorOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLineColor(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLineTypeNameOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLineTypeName(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLineTypeScaleOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLineTypeScale(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLinePrintWidthOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLinePrintWidth(v); }),
                OverrideFunc([&](int row) {return false; }, // 投影线Alpha
                [&](int row, bool v) { return; }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsTransparencyOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideTransparency(v); }),
                OverrideFunc([&](int row) {return false; }, // 投影线宽模式
                [&](int row, bool v) { return; }),
                dummyOverrideFunc, // 投影线颜色索引
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionLineSmartColorModeOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionLineSmartColorMode(v); }),
                dummyOverrideFunc, // 投影线最终颜色（只读）
            };

            OverrideFuncList overrideValidateFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineWidthValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineWidthValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineColorValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineColorValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineTypeNameValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineTypeNameValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineTypeScaleValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineTypeScaleValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLinePrintWidthValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLinePrintWidthValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsEnableProjectionLineColorAlphaValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetEnableProjectionLineColorAlphaValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsTransparencyValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetTransparencyValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineWidthModeValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineWidthModeValidity(v); }),
                dummyOverrideFunc,  // 投影线颜色索引
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionLineSmartColorModeValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionLineSmartColorModeValidity(v); }),
                dummyOverrideFunc,  // 投影线最终颜色（只读）
            };

            overrideFuncListList.push_back(overrideDummyFuncList);
            overrideFuncListList.push_back(overrideItemsFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
        }

        // 图形节点所在行
        static int s_rowNumOfGNodeSetDirectly = 8;
        for (int row = 0; row < s_numStyleLines; row++)
        {
            int column = 0;
            // 样式名称
            {
                gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(m_rowNames[row]));
                opItem->setFlags(Qt::ItemIsEnabled);
                m_table->setItem(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            }

            OwnerPtr<IGraphicsStyleData>& opDocGStyleData = m_gOverrideDataList[row]->m_opGStyleData;
            OverrideFuncList overrideFuncList = overrideFuncListList[row];

            // 如果图形节点上的样式是类别样式，则不能修改是否重载，否则该类别的图元可能显示错误。
            IGraphicsStyle* pGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(m_gOverrideDataList[row]->m_gStyleId));
            if (pGStyle && pGStyle->GetTargetCategoryUid().IsValid())
            {
                for (auto& iter : overrideFuncList)
                {
                    // 类别样式各项不能修改是否重载
                    iter.m_canOverride = false;
                }
            }

            //添加投影线宽
            column++;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineWidth(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineWidth(width); };
                ShowLineWidthCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //添加投影颜色
            column += 2;
            {
                GetColorFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineColor(); };
                SetColorFunc setFunc = [&](int row, gcmp::Color color) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //投影线型
            column += 2;
            {
                GetTextFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineTypeName(); };
                SetTextFunc setFunc = [&](int row, std::wstring txt) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineTypeName(txt); };
                ShowLineTypeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //投影线型缩放
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineTypeScale(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineTypeScale(width); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //投影打印线宽
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLinePrintWidth(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLinePrintWidth(width); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 投影线alpha是否有效
            column += 2;
            {
                GetBoolFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->IsProjectionLineColorAlphaEnabled(); };
                SetBoolFunc setFunc = [&](int row, bool enabled) { m_gOverrideDataList[row]->m_opGStyleData->EnableProjectionLineColorAlpha(enabled); };
                ShowEnabledCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 透明度
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetTransparency(); };
                SetDoubleFunc setFunc = [&](int row, double trans) { m_gOverrideDataList[row]->m_opGStyleData->SetTransparency(trans); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 投影线宽模式
            column += 2;
            {
                GetLineWidthModeFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineWidthMode(); };
                SetLineWidthModeFunc setFunc = [&](int row, gcmp::LineWidthMode mode) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineWidthMode(mode); };
                ShowLineWidthModeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 投影线颜色索引
            column += 2;
            {
                GetInt16Func func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineColorIndex(); };
                SetInt16Func setFunc = [&](int row, int16_t index) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 投影线智能颜色模式
            column += 2;
            {
                GetSmartColorModeFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionLineSmartColorMode(); };
                SetSmartColorModeFunc setFunc = [&](int row, gcmp::SmartColorMode mode) { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionLineSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 投影线最终颜色（只读显示）
            column += 2;
            {
                GetFinalColorFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalProjectionLineColor(); };
                ShowFinalColorCell(func, overrideFuncList[column/2+1], row, column);
            }

            // 图形节点重载的样式名称
            column +=2;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly); // 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeText || isGNodeRow)
                {
                    ShowTextCell(m_gOverrideDataList[row]->m_gNodeText, row, column, !isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
            // 图形节点重载的样式类别名称
            column ++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly);// 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeCategory || isGNodeRow)
                {
                    ShowCategoryCell(row, column, m_gOverrideDataList[row]->m_gNodeCategory, isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
        }
    }

    void GraphicsStyleTable::initSectionLines()
    {
        // 设置截面线样式表头
        const int s_countSectionLineColumns = 23; // 截面线相关列数（增加了更多属性）
        m_table->setColumnCount(s_countSectionLineColumns);
        m_table->setRowCount(s_numStyleLines);

        // 分别设置是否重载和重载项两列的宽度
        for (int i = 0; i < s_countSectionLineColumns - 1; i++)
        {
            if (i % 2 == 0) {
                m_table->setColumnWidth(i, 80);
            }
            else {
                m_table->setColumnWidth(i, 35);
            }
        }
        // 设置第一列和最后两列的宽度
        m_table->setColumnWidth(0, 130);
        m_table->setColumnWidth(21, 110);
        m_table->setColumnWidth(22, 80);

        {
            QStringList header;
            header << STR(GBMP_TR(L"样式重载名称")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"线宽"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"线型"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"缩放")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"截面打印线宽"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"透明度")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"宽模式"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色索引")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"智能颜色"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"最终颜色"))
                << STR(GBMP_TR(L"节点样式名称")) << STR(GBMP_TR(L"节点样式类别"));
            m_table->setHorizontalHeaderLabels(header);
        }

        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();

        // 类别级显示样式，没有是否可以重载的bool设置
        GetBoolFunc dummyGetFunc = [&](int row) { return false; };
        SetBoolFunc dummySetFunc = [&](int row, bool v) {};
        OverrideFunc dummyOverrideFunc(dummyGetFunc, dummySetFunc, false);

        // 是否可以重载的获得和设置回调函数
        OverrideFuncListList overrideFuncListList;
        {
            OverrideFuncList overrideDummyFuncList;
            for (int i = 0; i < s_countSectionLineColumns; i++) { overrideDummyFuncList.push_back(dummyOverrideFunc); };

            OverrideFuncList overrideItemsFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionLineWidthOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionLineWidth(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionLineColorOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionLineColor(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionLineTypeNameOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionLineTypeName(v); }),
                dummyOverrideFunc, // 截面线缩放
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionLinePrintWidthOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionLinePrintWidth(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsTransparencyOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideTransparency(v); }),
                dummyOverrideFunc, // 截面线宽模式
                dummyOverrideFunc, // 截面线颜色索引
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionLineSmartColorModeOverridden(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionLineSmartColorMode(v); }),
                dummyOverrideFunc, // 截面线最终颜色（只读）
            };

            OverrideFuncList overrideValidateFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineWidthValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineWidthValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineColorValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineColorValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineTypeNameValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineTypeNameValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineTypeScaleValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineTypeScaleValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLinePrintWidthValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLinePrintWidthValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsTransparencyValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetTransparencyValidity(v); }),
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineWidthModeValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineWidthModeValidity(v); }),
                dummyOverrideFunc,  // 截面线颜色索引
                OverrideFunc([&](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionLineSmartColorModeValid(); },
                [&](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionLineSmartColorModeValidity(v); }),
                dummyOverrideFunc,  // 截面线最终颜色（只读）
            };

            overrideFuncListList.push_back(overrideDummyFuncList);
            overrideFuncListList.push_back(overrideItemsFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
        }

        // 图形节点所在行
        static int s_rowNumOfGNodeSetDirectly = 8;
        for (int row = 0; row < s_numStyleLines; row++)
        {
            int column = 0;
            // 样式名称
            {
                gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(m_rowNames[row]));
                opItem->setFlags(Qt::ItemIsEnabled);
                m_table->setItem(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            }

            OwnerPtr<IGraphicsStyleData>& opDocGStyleData = m_gOverrideDataList[row]->m_opGStyleData;
            OverrideFuncList overrideFuncList = overrideFuncListList[row];

            // 如果图形节点上的样式是类别样式，则不能修改是否重载，否则该类别的图元可能显示错误。
            IGraphicsStyle* pGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(m_gOverrideDataList[row]->m_gStyleId));
            if (pGStyle && pGStyle->GetTargetCategoryUid().IsValid())
            {
                for (auto& iter : overrideFuncList)
                {
                    // 类别样式各项不能修改是否重载
                    iter.m_canOverride = false;
                }
            }

            // 截面线宽
            column++;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineWidth(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineWidth(width); };
                ShowLineWidthCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //添加截面线颜色
            column += 2;
            {
                GetColorFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineColor(); };
                SetColorFunc setFunc = [&](int row, gcmp::Color color) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 截面线型
            column += 2;
            {
                GetTextFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineTypeName(); };
                SetTextFunc setFunc = [&](int row, std::wstring txt) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineTypeName(txt); };
                ShowLineTypeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //截面线型缩放
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineTypeScale(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineTypeScale(width); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            //截面打印线宽
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLinePrintWidth(); };
                SetDoubleFunc setFunc = [&](int row, double width) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLinePrintWidth(width); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 透明度
            column += 2;
            {
                GetDoubleFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetTransparency(); };
                SetDoubleFunc setFunc = [&](int row, double trans) { m_gOverrideDataList[row]->m_opGStyleData->SetTransparency(trans); };
                ShowScaleCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 截面线宽模式
            column += 2;
            {
                GetLineWidthModeFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineWidthMode(); };
                SetLineWidthModeFunc setFunc = [&](int row, gcmp::LineWidthMode mode) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineWidthMode(mode); };
                ShowLineWidthModeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 截面线颜色索引
            column += 2;
            {
                GetInt16Func func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineColorIndex(); };
                SetInt16Func setFunc = [&](int row, int16_t index) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 截面线智能颜色模式
            column += 2;
            {
                GetSmartColorModeFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionLineSmartColorMode(); };
                SetSmartColorModeFunc setFunc = [&](int row, gcmp::SmartColorMode mode) { m_gOverrideDataList[row]->m_opGStyleData->SetSectionLineSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column/2+1], row, column);
            }

            // 截面线最终颜色（只读显示）
            column += 2;
            {
                GetFinalColorFunc func = [&](int row) { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalSectionLineColor(); };
                ShowFinalColorCell(func, overrideFuncList[column/2+1], row, column);
            }

            // 图形节点重载的样式名称
            column +=2;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly); // 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeText || isGNodeRow)
                {
                    ShowTextCell(m_gOverrideDataList[row]->m_gNodeText, row, column, !isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
            // 图形节点重载的样式类别名称
            column ++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly);// 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeCategory || isGNodeRow)
                {
                    ShowCategoryCell(row, column, m_gOverrideDataList[row]->m_gNodeCategory, isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
        }
    }

    void GraphicsStyleTable::initProjectionFaces()
    {
        // 设置投影面样式表头
        const int s_countProjectionFaceColumns = 29; // 投影面相关列数（增加了两个最终颜色列）
        m_table->setColumnCount(s_countProjectionFaceColumns);
        m_table->setRowCount(s_numStyleLines);

        // 分别设置是否重载和重载项两列的宽度
        for (int i = 0; i < s_countProjectionFaceColumns - 1; i++)
        {
            if (i % 2 == 0) {
                m_table->setColumnWidth(i, 80);
            }
            else {
                m_table->setColumnWidth(i, 35);
            }
        }
        // 设置第一列和最后四列的宽度
        m_table->setColumnWidth(0, 130);
        m_table->setColumnWidth(25, 80); // 投影面填充最终颜色
        m_table->setColumnWidth(26, 80); // 投影面最终颜色
        m_table->setColumnWidth(27, 80); // 节点样式名称
        m_table->setColumnWidth(28, 80);  // 节点样式类别

        {
            QStringList header;
            header << STR(GBMP_TR(L"样式重载名称")) 
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充图案")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充缩放"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充旋转")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充线宽"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充打印线宽")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充Alpha"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充颜色索引"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充智能颜色"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色索引")) 
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"智能颜色"))
                << STR(GBMP_TR(L"填充最终颜色")) << STR(GBMP_TR(L"最终颜色")) << STR(GBMP_TR(L"节点样式名称")) << STR(GBMP_TR(L"节点样式类别"));
            m_table->setHorizontalHeaderLabels(header);
        }

        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();

        // 虚拟的重载函数，用于不需要重载的列
        GetBoolFunc dummyGetFunc = [](int row) { return false; };
        SetBoolFunc dummySetFunc = [](int row, bool v) { return; };
        OverrideFunc dummyOverrideFunc(dummyGetFunc, dummySetFunc, false);

        // 是否可以重载的获得和设置回调函数
        OverrideFuncListList overrideFuncListList;
        {
            OverrideFuncList overrideDummyFuncList;
            for (int i = 0; i < s_countProjectionFaceColumns; i++) { overrideDummyFuncList.push_back(dummyOverrideFunc); };

            OverrideFuncList overrideItemsFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionFaceHatchPatternOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionFaceHatchPattern(v); }),
                dummyOverrideFunc, // 投影面填充缩放
                dummyOverrideFunc, // 投影面填充旋转
                dummyOverrideFunc, // 投影面填充线宽
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionFaceHatchLinePrintWidthOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionFaceHatchLinePrintWidth(v); }),
                dummyOverrideFunc, // 投影面Alpha
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionFaceHatchColorOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionFaceHatchColor(v); }),
                dummyOverrideFunc, // 投影面填充颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsProjectionFaceHatchSmartColorModeOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideProjectionFaceHatchSmartColorMode(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsColorOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideColor(v); }),
                dummyOverrideFunc, // 投影面颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSmartColorModeOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSmartColorMode(v); }),
                dummyOverrideFunc, // 投影面填充最终颜色（只读，不需要重载）
                dummyOverrideFunc, // 投影面最终颜色（只读，不需要重载）
                dummyOverrideFunc, // 索引12：节点样式名称列，不需要重载
            };

            OverrideFuncList overrideValidateFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchPatternValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchPatternValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchScaleValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchScaleValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchRotationValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchRotationValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchLineWidthValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchLineWidthValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchLinePrintWidthValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchLinePrintWidthValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsEnableProjectionFaceHatchColorAlphaValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetEnableProjectionFaceHatchColorAlphaValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchColorValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchColorValidity(v); }),
                dummyOverrideFunc, // 投影面填充颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsProjectionFaceHatchSmartColorModeValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetProjectionFaceHatchSmartColorModeValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsColorValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetColorValidity(v); }),
                dummyOverrideFunc, // 投影面颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSmartColorModeValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSmartColorModeValidity(v); }),
                dummyOverrideFunc, // 投影面填充最终颜色（只读，不需要验证）
                dummyOverrideFunc, // 投影面最终颜色（只读，不需要验证）
                dummyOverrideFunc, // 索引12：节点样式名称列，不需要重载
            };

            overrideFuncListList.push_back(overrideDummyFuncList);
            overrideFuncListList.push_back(overrideItemsFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
        }

        // 图形节点所在行
        static int s_rowNumOfGNodeSetDirectly = 8;
        for (int row = 0; row < s_numStyleLines; row++)
        {
            OverrideFuncList& overrideFuncList = overrideFuncListList[row];
            int column = 0;

            // 样式重载名称
            {
                gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(m_rowNames[row]));
                opItem->setFlags(Qt::ItemIsEnabled);
                m_table->setItem(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            }

            OwnerPtr<IGraphicsStyleData>& opDocGStyleData = m_gOverrideDataList[row]->m_opGStyleData;

            // 如果图形节点上的样式是类别样式，则不能修改是否重载，否则该类别的图元可能显示错误。
            IGraphicsStyle* pGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(m_gOverrideDataList[row]->m_gStyleId));
            if (pGStyle && pGStyle->GetTargetCategoryUid().IsValid())
            {
                for (auto& iter : overrideFuncList)
                {
                    // 类别样式各项不能修改是否重载
                    iter.m_canOverride = false;
                }
            }

            // 投影面填充图案
            column ++;
            {
                auto func = [this](int row) -> std::wstring { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchPattern(); };
                auto setFunc = [this](int row, std::wstring pattern) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchPattern(pattern); };
                ShowHatchPatternCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充缩放
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchScale(); };
                auto setFunc = [this](int row, double scale) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchScale(scale); };
                ShowScaleCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充旋转
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchRotation(); };
                auto setFunc = [this](int row, double rotation) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchRotation(rotation); };
                ShowRotateCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充线宽
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchLineWidth(); };
                auto setFunc = [this](int row, double width) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchLineWidth(width); };
                ShowLineWidthCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充打印线宽
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchLinePrintWidth(); };
                auto setFunc = [this](int row, double width) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchLinePrintWidth(width); };
                ShowScaleCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }


            // 投影面填充Alpha启用
            column += 2;
            {
                auto func = [this](int row) -> bool { return m_gOverrideDataList[row]->m_opGStyleData->IsProjectionFaceHatchColorAlphaEnabled(); };
                auto setFunc = [this](int row, bool enabled) -> void { m_gOverrideDataList[row]->m_opGStyleData->EnableProjectionFaceHatchColorAlpha(enabled); };
                ShowEnabledCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充颜色
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchColor(); };
                auto setFunc = [this](int row, gcmp::Color color) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }


            // 投影面填充颜色索引
            column += 2;
            {
                auto func = [this](int row) -> int16_t { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchColorIndex(); };
                auto setFunc = [this](int row, int16_t index) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充智能颜色模式
            column += 2;
            {
                auto func = [this](int row) -> gcmp::SmartColorMode { return m_gOverrideDataList[row]->m_opGStyleData->GetProjectionFaceHatchSmartColorMode(); };
                auto setFunc = [this](int row, gcmp::SmartColorMode mode) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetProjectionFaceHatchSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面颜色
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetColor(); };
                auto setFunc = [this](int row, gcmp::Color color) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }


            // 投影面颜色索引
            column += 2;
            {
                auto func = [this](int row) -> int16_t { return m_gOverrideDataList[row]->m_opGStyleData->GetColorIndex(); };
                auto setFunc = [this](int row, int16_t index) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面智能颜色模式
            column += 2;
            {
                auto func = [this](int row) -> gcmp::SmartColorMode { return m_gOverrideDataList[row]->m_opGStyleData->GetSmartColorMode(); };
                auto setFunc = [this](int row, gcmp::SmartColorMode mode) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 投影面填充最终颜色（只读显示）
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalProjectionFaceHatchColor(); };
                ShowFinalColorCellWithoutCheckbox(func, row, column);
            }

            // 投影面最终颜色（只读显示）
            column ++;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalColor(); };
                ShowFinalColorCellWithoutCheckbox(func, row, column);
            }

            // 图形节点重载的样式名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly); // 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeText || isGNodeRow)
                {
                    ShowTextCell(m_gOverrideDataList[row]->m_gNodeText, row, column, !isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
            // 图形节点重载的样式类别名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly);// 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeCategory || isGNodeRow)
                {
                    ShowCategoryCell(row, column, m_gOverrideDataList[row]->m_gNodeCategory, isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
        }
    }

    void GraphicsStyleTable::initSectionFaces()
    {
        // 设置截面面样式表头
        const int s_countSectionFaceColumns = 25; // 截面面相关列数（增加了两个最终颜色列）
        m_table->setColumnCount(s_countSectionFaceColumns);
        m_table->setRowCount(s_numStyleLines);

        // 分别设置是否重载和重载项两列的宽度
        for (int i = 0; i < s_countSectionFaceColumns - 1; i++)
        {
            if (i % 2 == 0) {
                m_table->setColumnWidth(i, 80);
            }
            else {
                m_table->setColumnWidth(i, 35);
            }
        }
        // 设置第一列和最后四列的宽度
        m_table->setColumnWidth(0, 130);
        m_table->setColumnWidth(21, 80); // 截面填充最终颜色
        m_table->setColumnWidth(22, 80); // 截面最终颜色
        m_table->setColumnWidth(23, 80); // 节点样式名称
        m_table->setColumnWidth(24, 80);  // 节点样式类别

        {
            QStringList header;
            header << STR(GBMP_TR(L"样式重载名称"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充图案")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充缩放"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充旋转")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充Alpha"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充颜色"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充颜色索引")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"填充智能颜色"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色")) << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"颜色索引"))
                << STR(GBMP_TR(L"重载")) << STR(GBMP_TR(L"智能颜色"))
                << STR(GBMP_TR(L"填充最终颜色"))<< STR(GBMP_TR(L"最终颜色"))
                << STR(GBMP_TR(L"节点样式名称")) << STR(GBMP_TR(L"节点样式类别"));
            m_table->setHorizontalHeaderLabels(header);
        }

        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();

        // 虚拟的重载函数，用于不需要重载的列
        GetBoolFunc dummyGetFunc = [](int row) { return false; };
        SetBoolFunc dummySetFunc = [](int row, bool v) { return; };
        OverrideFunc dummyOverrideFunc(dummyGetFunc, dummySetFunc, false);

        // 是否可以重载的获得和设置回调函数
        OverrideFuncListList overrideFuncListList;
        {
            OverrideFuncList overrideDummyFuncList;
            for (int i = 0; i < s_countSectionFaceColumns; i++) { overrideDummyFuncList.push_back(dummyOverrideFunc); };

            OverrideFuncList overrideItemsFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionFaceHatchPatternOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionFaceHatchPattern(v); }),
                dummyOverrideFunc, // 截面填充缩放
                dummyOverrideFunc, // 截面填充旋转
                dummyOverrideFunc, // 截面填充Alpha
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionFaceHatchColorOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionFaceHatchColor(v); }),
                dummyOverrideFunc, // 截面填充颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionFaceHatchSmartColorModeOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionFaceHatchSmartColorMode(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionFaceColorOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionFaceColor(v); }),
                dummyOverrideFunc, // 截面颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->IsSectionFaceColorSmartColorModeOverridden(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataOverrideItems->OverrideSectionFaceColorSmartColorMode(v); }),
                dummyOverrideFunc, // 截面填充最终颜色（只读，不需要重载）
                dummyOverrideFunc, // 截面最终颜色（只读，不需要重载）
                dummyOverrideFunc, // 索引13：节点样式名称列，不需要重载
            };

            OverrideFuncList overrideValidateFuncList = {
                dummyOverrideFunc, // 索引0：样式名称列，不需要重载
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceHatchPatternValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceHatchPatternValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceHatchScaleValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceHatchScaleValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceHatchRotationValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceHatchRotationValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsEnableSectionFaceHatchColorAlphaValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetEnableSectionFaceHatchColorAlphaValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceHatchColorValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceHatchColorValidity(v); }),
                dummyOverrideFunc, // 截面填充颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceHatchSmartColorModeValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceHatchSmartColorModeValidity(v); }),
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSectionFaceColorValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSectionFaceColorValidity(v); }),
                dummyOverrideFunc, // 截面颜色索引
                OverrideFunc([this](int row) {return m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->IsSmartColorModeValid(); },
                [this](int row, bool v) { m_gOverrideDataList[row]->m_opGStyleDataValiditySettings->SetSmartColorModeValidity(v); }),
                dummyOverrideFunc, // 截面填充最终颜色（只读，不需要验证）
                dummyOverrideFunc, // 截面最终颜色（只读，不需要验证）
                dummyOverrideFunc, // 索引13：节点样式名称列，不需要重载
            };

            overrideFuncListList.push_back(overrideDummyFuncList);
            overrideFuncListList.push_back(overrideItemsFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
            overrideFuncListList.push_back(overrideValidateFuncList);
        }

        // 图形节点所在行
        static int s_rowNumOfGNodeSetDirectly = 8;
        for (int row = 0; row < s_numStyleLines; row++)
        {
            OverrideFuncList& overrideFuncList = overrideFuncListList[row];
            int column = 0;

            // 样式重载名称
            {
                gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(m_rowNames[row]));
                opItem->setFlags(Qt::ItemIsEnabled);
                m_table->setItem(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            }

            OwnerPtr<IGraphicsStyleData>& opDocGStyleData = m_gOverrideDataList[row]->m_opGStyleData;

            // 如果图形节点上的样式是类别样式，则不能修改是否重载，否则该类别的图元可能显示错误。
            IGraphicsStyle* pGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(m_gOverrideDataList[row]->m_gStyleId));
            if (pGStyle && pGStyle->GetTargetCategoryUid().IsValid())
            {
                for (auto& iter : overrideFuncList)
                {
                    // 类别样式各项不能修改是否重载
                    iter.m_canOverride = false;
                }
            }

            // 截面填充图案
            column ++;
            {
                auto func = [this](int row) -> std::wstring { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchPattern(); };
                auto setFunc = [this](int row, std::wstring pattern) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchPattern(pattern); };
                ShowHatchPatternCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充缩放
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchScale(); };
                auto setFunc = [this](int row, double scale) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchScale(scale); };
                ShowScaleCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充旋转
            column += 2;
            {
                auto func = [this](int row) -> double { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchRotation(); };
                auto setFunc = [this](int row, double rotation) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchRotation(rotation); };
                ShowRotateCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充Alpha启用
            column += 2;
            {
                auto func = [this](int row) -> bool { return m_gOverrideDataList[row]->m_opGStyleData->IsSectionFaceHatchColorAlphaEnabled(); };
                auto setFunc = [this](int row, bool enabled) -> void { m_gOverrideDataList[row]->m_opGStyleData->EnableSectionFaceHatchColorAlpha(enabled); };
                ShowEnabledCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充颜色
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchColor(); };
                auto setFunc = [this](int row, gcmp::Color color) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充颜色索引
            column += 2;
            {
                auto func = [this](int row) -> int16_t { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchColorIndex(); };
                auto setFunc = [this](int row, int16_t index) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充智能颜色模式
            column += 2;
            {
                auto func = [this](int row) -> gcmp::SmartColorMode { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceHatchSmartColorMode(); };
                auto setFunc = [this](int row, gcmp::SmartColorMode mode) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceHatchSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面颜色
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceColor(); };
                auto setFunc = [this](int row, gcmp::Color color) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceColor(color); };
                ShowColorCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面颜色索引
            column += 2;
            {
                auto func = [this](int row) -> int16_t { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceColorIndex(); };
                auto setFunc = [this](int row, int16_t index) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceColorIndex(index); };
                ShowColorIndexCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面智能颜色模式
            column += 2;
            {
                auto func = [this](int row) -> gcmp::SmartColorMode { return m_gOverrideDataList[row]->m_opGStyleData->GetSectionFaceColorSmartColorMode(); };
                auto setFunc = [this](int row, gcmp::SmartColorMode mode) -> void { m_gOverrideDataList[row]->m_opGStyleData->SetSectionFaceColorSmartColorMode(mode); };
                ShowSmartColorModeCell(func, setFunc, overrideFuncList[column / 2 + 1], row, column);
            }

            // 截面填充最终颜色（只读显示）
            column += 2;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalSectionFaceHatchColor(); };
                ShowFinalColorCellWithoutCheckbox(func, row, column);
            }

            // 截面最终颜色（只读显示）
            column ++;
            {
                auto func = [this](int row) -> gcmp::Color { return m_gOverrideDataList[row]->m_opGStyleData->GetFinalSectionFaceColor(); };
                ShowFinalColorCellWithoutCheckbox(func, row, column);
            }

            // 图形节点重载的样式名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly); // 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeText || isGNodeRow)
                {
                    ShowTextCell(m_gOverrideDataList[row]->m_gNodeText, row, column, !isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
            // 图形节点重载的样式类别名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly);// 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideDataList[row]->m_hasGNodeCategory || isGNodeRow)
                {
                    ShowCategoryCell(row, column, m_gOverrideDataList[row]->m_gNodeCategory, isGNodeRow);
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
        }
    }

    std::vector<std::wstring> GraphicsStyleTable::CalcItemNames_inMaterialComboBox()
    {
        std::vector<std::wstring> itemNames;
        // 文档中所有材质实例(material guid)
        std::vector<IGraphicsMaterial*> graphicsMaterials = IGraphicsMaterial::GetAllGraphicsMaterials(m_wpUIDoc->GetDbDocument());
        for (const IGraphicsMaterial* pGraphicsMaterial : graphicsMaterials)
        {
            // 针对类别的材质和材质实例不是一一绑定的
            UniIdentity categoryId = pGraphicsMaterial->GetTargetCategoryUid();
            bool isCategoryMaterial = categoryId.IsValid();
            if (isCategoryMaterial && !pGraphicsMaterial->IsBoundWithMaterialInstance())
            {
                continue;
            }

            std::wstring materialName;
            bool isOk = pGraphicsMaterial->GetMaterialInstanceName(materialName);
            DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-09-30");
            if (isCategoryMaterial)
            {
                const ICategory* pTargetCategory = ICategoryLibrary::Get(m_wpUIDoc->GetDbDocument())->GetCategory(categoryId);
                materialName += L"[" + pTargetCategory->GetDisplayName() + L"]";;
            }
            itemNames.push_back(materialName);
        }

        itemNames.push_back(MORE_MATERIAL);
        itemNames.push_back(NO_MATERIAL);

        return itemNames;
    }

    void GraphicsStyleTable::OnMaterialComboBox_currentIndexChanged(int index)
    {
        IDocument* pDoc = m_wpUIDoc->GetDbDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"m_wpUIDoc->GetDbDocument() 返回空指针",L"GDMPLab",L"2024-09-30");

        // 当前类别的材质element
        int col = 1;
        int row = sender()->property("row").toInt();
        QComboBox* pComboBox = (QComboBox*)m_table->cellWidget(row, col);
        int iIndex = pComboBox->currentIndex();
        std::wstring itemText = pComboBox->itemText(iIndex).toStdWString();
        if (itemText == MORE_MATERIAL)
        {
            //日志回放时，忽略对材质的设置
            if (gcmp::JournalUtils::IsInReplay())
                return;

            OwnerPtr<IUserTransaction> opUt = IUserTransaction::Create(pDoc, GBMP_TR(L"修改图形显示样式"));
            DBG_WARN_AND_RETURN_VOID_UNLESS(opUt, L"事务创建失败",L"GDMPLab",L"2024-09-30");

            // 选择材质实例
            ElementId newMaterialElementId;
            UiCommonDialog::ButtonType res = UiCommonDialog::ButtonType::Invalid;
            ElementId currentMaterialId = m_gOverrideMaterialDataList[row]->m_gNodeMaterialId;
            res = UiCommonDialog::ShowMaterialSelectionDialog(pDoc, newMaterialElementId, L"", 
                L"", L"", 
                UiCommonDialog::MaterialSelecitonDialogTheme::White, currentMaterialId, L"木纹");
            if (res == UiCommonDialog::ButtonType::OK && newMaterialElementId.IsValid())
            {
                IGraphicsMaterial* pGraphicsMaterial = quick_cast<IGraphicsMaterial>(pDoc->GetElement(newMaterialElementId));
                DBG_WARN_AND_RETURN_VOID_UNLESS(pGraphicsMaterial, L"DocUtil::GetElement<IGraphicsMaterial>(pDoc, newMaterialElementId) 返回空指针",L"GDMPLab",L"2024-09-30");

                std::wstring materialName;
                bool isOk = pGraphicsMaterial->GetMaterialInstanceName(materialName);
                DBG_WARN_AND_RETURN_VOID_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-09-30");

                // 新增了一个材质，所有的下拉列表都需更新
                UpdateComboBoxs_4_materialAdded(row, materialName);

                // 记录修改
                m_gOverrideMaterialDataList[row]->m_materialId = newMaterialElementId;
            }

            opUt->Commit();
        }
        else if (itemText == NO_MATERIAL)
        {
            m_gOverrideMaterialDataList[row]->m_materialId = ElementId::InvalidID; // 记录下材质的修改
        }
        else
        {
            bool isMaterialFound = false;

            std::vector<IGraphicsMaterial*> graphicsMaterials = IGraphicsMaterial::GetAllGraphicsMaterials(m_wpUIDoc->GetDbDocument());
            for (const IGraphicsMaterial* pGraphicsMaterial : graphicsMaterials)
            {
                // 针对类别的材质和材质实例不是一一绑定的，所以忽略
                UniIdentity categoryId = pGraphicsMaterial->GetTargetCategoryUid();
                bool isCategoryMaterial = categoryId.IsValid();
                if (isCategoryMaterial && !pGraphicsMaterial->IsBoundWithMaterialInstance())
                {
                    continue;
                }

                std::wstring newMaterialName;
                bool isOk = pGraphicsMaterial->GetMaterialInstanceName(newMaterialName);
                DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-09-30");
                if (isCategoryMaterial)
                {
                    const ICategory* pTargetCategory = ICategoryLibrary::Get(pDoc)->GetCategory(categoryId);
                    newMaterialName += L"[" + pTargetCategory->GetDisplayName() + L"]";
                }
                if (newMaterialName == itemText)
                {
                    m_gOverrideMaterialDataList[row]->m_materialId = pGraphicsMaterial->GetElementId(); // 记录下材质的修改
                    isMaterialFound = true;
                    break;
                }
            }

            DBG_WARN_UNLESS(isMaterialFound, L"没有匹配的材质？",L"GDMPLab",L"2024-09-30");
        }
    }

    void GraphicsStyleTable::UpdateComboBoxs_4_materialAdded(int categoryIndex, const std::wstring& materialName)
    {
        // 计算出材质下拉列表中可见的内容
        std::vector<std::wstring> itemNames = CalcItemNames_inMaterialComboBox();

        FOR_EACH(materialComboBox, m_materialComboBoxList)
        {
            int row = materialComboBox->property("row").toInt();
            int col = materialComboBox->property("col").toInt();
            if (row == categoryIndex) // 就是这个下拉列表中新增了材质
            {
                // 计算下拉列表的当前项
                QStringList itemNames_4_qt;
                int currentComboBoxIndex = 0, iIndex = 0;
                FOR_EACH(itemName, itemNames)
                {
                    itemNames_4_qt.push_back(QString::fromStdWString(itemName));
                    if (itemName == materialName)
                    {
                        currentComboBoxIndex = iIndex;
                    }

                    iIndex++;
                }

                materialComboBox->blockSignals(true);
                materialComboBox->clear();
                materialComboBox->addItems(itemNames_4_qt);
                materialComboBox->setCurrentIndex(currentComboBoxIndex);
                materialComboBox->blockSignals(false);
            }
            else
            {
                std::wstring oldMaterialName = materialComboBox->currentText().toStdWString();

                // 计算下拉列表的当前项
                QStringList itemNames_4_qt;
                int currentComboBoxIndex = 0, iIndex = 0;
                FOR_EACH(itemName, itemNames)
                {
                    itemNames_4_qt.push_back(QString::fromStdWString(itemName));
                    if (itemName == oldMaterialName)
                    {
                        currentComboBoxIndex = iIndex;
                    }

                    iIndex++;
                }

                materialComboBox->blockSignals(true);
                materialComboBox->clear();
                materialComboBox->addItems(itemNames_4_qt);
                materialComboBox->setCurrentIndex(currentComboBoxIndex);
                materialComboBox->blockSignals(false);
            }
        }
    }

    void GraphicsStyleTable::InitMaterialCell(int rowIndex)
    {
        gcmp::OwnerPtr<MaterialOverrideData>& materialOverrideData = m_gOverrideMaterialDataList[rowIndex];

        if(!materialOverrideData->m_available)
        {
            OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
            opItem->setFlags(opItem->flags() & (~Qt::ItemIsEnabled));
            opItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
            m_table->setItem(rowIndex, 1, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            return;
        }

        IDocument* pDoc = m_wpUIDoc->GetDbDocument();
        // 计算出材质下拉列表中可见的内容
        std::vector<std::wstring> itemNames = CalcItemNames_inMaterialComboBox();

        ElementId materialId = materialOverrideData->m_materialId;
        std::wstring materialName = GBMP_TR(L"无");
        if (materialId.IsValid())
        {
            IElement* pElement = m_wpUIDoc->GetDbDocument()->GetElement(materialId);
            DBG_WARN_AND_RETURN_VOID_UNLESS(pElement != nullptr, L"pElement为空",L"GDMPLab",L"2024-09-30");
            IGraphicsMaterial* pMaterialElement = quick_cast<IGraphicsMaterial>(pElement);
            DBG_WARN_AND_RETURN_VOID_UNLESS(pMaterialElement != nullptr, L"打开 IGraphicsMaterial 对象失败",L"GDMPLab",L"2024-09-30");

            pMaterialElement->GetMaterialInstanceName(materialName);

            UniIdentity categoryId = pMaterialElement->GetTargetCategoryUid();
            if (categoryId.IsValid())
            {
                const ICategory* pTargetCategory = ICategoryLibrary::Get(pDoc)->GetCategory(categoryId);
                materialName += L"[" + pTargetCategory->GetDisplayName() + L"]";
            }
        }

        // 计算下拉列表的当前项
        QStringList itemNames_4_qt;
        int currentComboBoxIndex = 0, iIndex = 0;
        FOR_EACH(itemName, itemNames)
        {
            itemNames_4_qt.push_back(QString::fromStdWString(itemName));
            if (itemName == materialName)
            {
                currentComboBoxIndex = iIndex;
            }

            iIndex++;
        }

        // 创建下拉列表
        gcmp::OwnerPtr<QComboBox> opComboBox = NEW_AS_OWNER_PTR(QComboBox);
        opComboBox->addItems(itemNames_4_qt);
        opComboBox->setCurrentIndex(currentComboBoxIndex);
        opComboBox->setProperty("row", rowIndex);
        connect(opComboBox.get(), SIGNAL(currentIndexChanged(int)), this, SLOT(OnMaterialComboBox_currentIndexChanged(int)));
        m_materialComboBoxList.push_back(opComboBox.get());
        m_table->setCellWidget(rowIndex, 1, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opComboBox));
    }

    void GraphicsStyleTable::initMaterial()
    {
        // 获取样式类型
        // 设置样式表头
        m_table->setColumnCount(s_countMaterialPageColumns);
        m_table->setRowCount(s_numStyleLines);

        // 分别设置是否重载和重载项两列的宽度
        for (int i = 0; i < s_countMaterialPageColumns; i++)
        {
            m_table->setColumnWidth(i, 150);
        }
        {
            QStringList header;
            header << STR(GBMP_TR(L"样式重载名称")) << STR(GBMP_TR(L"材质")) << STR(GBMP_TR(L"节点材质名称")) << STR(GBMP_TR(L"节点材质类别")) << STR(GBMP_TR(L""));
            m_table->setHorizontalHeaderLabels(header);
        }
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_wpUIDoc, L"m_wpUIDoc == nullptr",L"GDMPLab",L"2024-09-30");
        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-09-30");

        // 类别级显示样式，没有是否可以重载的bool设置
        GetBoolFunc dummyGetFunc = [&](int row) { return false; };
        SetBoolFunc dummySetFunc = [&](int row, bool v) {};
        OverrideFunc dummyOverrideFunc(dummyGetFunc, dummySetFunc, false);

        // 图形节点所在行
        static int s_rowNumOfGNodeSetDirectly = 8;
        for (int row = 0; row < s_numStyleLines; row++)
        {
            int column = 0;
            // 材质名称
            {
                std::wstring rowHeadName =  m_rowNames[row];
                StringUtil::ReplaceAll(rowHeadName, L"：回调", L"");
                gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(rowHeadName));
                opItem->setFlags(Qt::ItemIsEnabled);
                m_table->setItem(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
            }

            //材质设置
            column++;
            {
                InitMaterialCell(row);
            }

            // 图形节点重载的材质名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly); // 仅直接设置图形节点样式时，可以修改
                if (m_gOverrideMaterialDataList[row]->m_hasGNodeText || isGNodeRow)
                {
                    gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                    opLineEdit->setText(QString::fromStdWString(m_gOverrideMaterialDataList[row]->m_gNodeText));
                    opLineEdit->setEnabled(isGNodeRow);
                    m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
            // 图形节点重载的材质类别名称
            column++;
            {
                bool isGNodeRow = (row == s_rowNumOfGNodeSetDirectly);// 仅直接设置图形节点样式时
                if (m_gOverrideMaterialDataList[row]->m_hasGNodeMaterialCategory)
                {
                    const ICategory* pCateory = m_gOverrideMaterialDataList[row]->m_gNodeCategory;
                    gcmp::OwnerPtr<QLineEdit> opLineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                    opLineEdit->setText(QString::fromStdWString(pCateory->GetDisplayName()));
                    opLineEdit->setEnabled(false);
                    m_table->setCellWidget(row, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opLineEdit));
                }
                else
                {
                    MakeCellDisable(row, column);
                }
            }
        }
    }
#pragma endregion

#pragma region UI事件
    void GraphicsStyleTable::OnClickColorCell(int bnt)
    {
        QPushButton *colorButton = dynamic_cast<QPushButton*>(m_opColorBtnGroup->button(bnt));
        int column, row;
        std::tie(column, row) = GetIndex(bnt);
        QColor qColor = m_colorMap[UVIndex(row, 2)];
        QColor resColor;

        //文档模式下直接打开颜色对话框获取颜色
        QColorDialog* colorwidget = NEW_AS_QT_CHILD(QColorDialog, m_parent);
        colorwidget->setCurrentColor(qColor);
        resColor = colorwidget->getColor(qColor, m_parent, QString::fromStdWString(GBMP_TR(L"选择颜色")), QColorDialog::ShowAlphaChannel);
        if (resColor.isValid())
        {
            colorButton->setStyleSheet(QString("background-color:%1").arg(resColor.name()));
        }
        else
        {
            colorButton->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
        }
        //删除默认文字
        if (colorButton->text() == STR(L"Default"))
        {
            colorButton->setText(QString::Null());
        }

        m_colorMap[UVIndex(bnt, 2)] = resColor.isValid() ? resColor : qColor;
    }

    void GraphicsStyleTable::OnClickLineTypeCell(int iButtonIndex)
    {
        // 选择线型。
        LineTypeDlg dlg;
        if (dlg.exec() == QDialog::Accepted)
        {
            QString selectLineTypeName = dlg.GetSelectLineTypeName();
            QPushButton* pLineTypeButton = dynamic_cast<QPushButton*>(m_opLineTypeBtnGroup->button(iButtonIndex));
            pLineTypeButton->setText(selectLineTypeName);
        }
    }

    void GraphicsStyleTable::OnApplied()
    {
        if (m_wpUIDoc.Get() == nullptr)
        {
            return;
        }

        gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-09-30");

        if (m_tableType == TableType::ProjectionLines)
        {
            for (int row = 0; row < 9; row++)
            {
                if (!IsAvailable(row)) continue; // 跳过不可用的行

                int column = 0;
                //设置投影线宽
                column += 2;
                SetLineWidthCell(row, column);
                column += 2;
                SetColorCell(row, column);
                column += 2;
                SetLineTypeCell(row, column);
                column += 2;
                SetScaleCell(row, column);
                column += 2;
                SetScaleCell(row, column); // 投影打印线宽
                column += 2;
                SetEnabledCell(row, column); // 投影线Alpha
                column += 2;
                SetScaleCell(row, column); // 透明度
                column += 2;
                SetLineWidthModeCell(row, column); // 投影线宽模式
                column += 2;
                SetColorIndexCell(row, column); // 投影线颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 投影线智能颜色模式
                column += 2;
                // 投影线最终颜色是只读的，不需要设置
                // 图形节点上样式的名称
                column++;
                m_gOverrideDataList[row]->m_gNodeText = GetTextCell(row, column);
                column++;
                {
                    // 图形节点上样式的类型
                    SetCategoryCell(row, column);
                }
            }
        }
        else if (m_tableType == TableType::SectionLines)
        {
            for (int row = 0; row < 9; row++)
            {
                if (!IsAvailable(row)) continue; // 跳过不可用的行

                int column = 0;
                //设置截面线宽
                column += 2;
                SetLineWidthCell(row, column);
                column += 2;
                SetColorCell(row, column);
                column += 2;
                SetLineTypeCell(row, column);
                column += 2;
                SetScaleCell(row, column);
                column += 2;
                SetScaleCell(row, column); // 截面打印线宽
                column += 2;
                SetScaleCell(row, column); // 透明度
                column += 2;
                SetLineWidthModeCell(row, column); // 截面线宽模式
                column += 2;
                SetColorIndexCell(row, column); // 截面线颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 截面线智能颜色模式
                column += 2;
                // 截面线最终颜色是只读的，不需要设置
                // 图形节点上样式的名称
                column++;
                m_gOverrideDataList[row]->m_gNodeText = GetTextCell(row, column);
                column++;
                {
                    // 图形节点上样式的类型
                    SetCategoryCell(row, column);
                }
            }
        }
        else if (m_tableType == TableType::ProjectionFaces)
        {
            for (int row = 0; row < 9; row++)
            {
                if (!IsAvailable(row)) continue; // 跳过不可用的行

                int column = 0;
                column += 2;
                SetHatchPatternCell(row, column); // 投影面填充图案
                column += 2;
                SetScaleCell(row, column); // 投影面填充缩放
                column += 2;
                SetRotateCell(row, column); // 投影面填充旋转
                column += 2;
                SetLineWidthCell(row, column); // 投影面填充线宽
                column += 2;
                SetScaleCell(row, column); // 投影面填充打印线宽
                column += 2;
                SetEnabledCell(row, column); // 投影面Alpha
                column += 2;
                SetColorCell(row, column);  // 投影面填充颜色
                column += 2;
                SetColorIndexCell(row, column); // 投影面填充颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 投影面填充智能颜色
                column += 2;
                SetColorCell(row, column);  // 投影面颜色
                column += 2;
                SetColorIndexCell(row, column); // 投影面颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 投影面智能颜色
                column += 2;
                // 投影面填充最终颜色是只读的，不需要设置
                column++;
                // 投影面最终颜色是只读的，不需要设置
                column++;
                // 图形节点上样式的名称
                m_gOverrideDataList[row]->m_gNodeText = GetTextCell(row, column);
                column++;
                {
                    // 图形节点上样式的类型
                    SetCategoryCell(row, column);
                }
            }
        }
        else if (m_tableType == TableType::SectionFaces)
        {
            for (int row = 0; row < 9; row++)
            {
                if (!IsAvailable(row)) continue; // 跳过不可用的行

                int column = 0;
                column += 2;
                SetHatchPatternCell(row, column); // 截面填充图案
                column += 2;
                SetScaleCell(row, column); // 截面填充缩放
                column += 2;
                SetRotateCell(row, column); // 截面填充旋转
                column += 2;
                SetEnabledCell(row, column); // 截面填充Alpha
                column += 2;
                SetColorCell(row, column);                // 截面填充颜色
                column += 2;
                SetColorIndexCell(row, column); // 截面填充颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 截面填充智能颜色
                column += 2;
                SetColorCell(row, column); // 截面颜色
                column += 2;
                SetColorIndexCell(row, column); // 截面颜色索引
                column += 2;
                SetSmartColorModeCell(row, column); // 截面智能颜色
                column += 2;
                // 截面填充最终颜色是只读的，不需要设置
                column++;
                // 截面最终颜色是只读的，不需要设置
                column++;
                // 图形节点上样式的名称
                m_gOverrideDataList[row]->m_gNodeText = GetTextCell(row, column);
                column++;
                {
                    // 图形节点上样式的类型
                    SetCategoryCell(row, column);
                }
            }
        }
        else if (m_tableType == TableType::Material)
        {
            for (int row = 0; row < 9; row++)
            {
                m_gOverrideMaterialDataList[row]->m_gNodeText = GetTextCell(row, 2);
            }
        }
    }

#pragma endregion
}


